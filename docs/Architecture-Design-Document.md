# Curio API 现代化后端架构设计文档

## 📋 文档信息

- **项目名称**: Curio API
- **架构模式**: DDD + CQRS + Event Sourcing + Orleans
- **创建日期**: 2025-08-24
- **版本**: v1.0

## 🎯 架构概览

### 核心设计理念

本项目采用现代化的分布式架构，结合以下核心模式：

- **Orleans**: 作为分布式计算和状态管理的核心引擎
- **Event Sourcing**: 通过事件流记录所有状态变更
- **CQRS**: 命令查询职责分离，优化读写性能
- **DDD**: 领域驱动设计，构建丰富的业务模型
- **消息驱动**: 异步处理和松耦合通信

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway / Load Balancer              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 ASP.NET Core Web API                        │
│              (Controllers + SignalR Hubs)                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Orleans Silo Cluster                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Domain    │ │   CQRS      │ │   Orleans Streams       │ │
│  │   Grains    │ │ Handlers    │ │   (Kafka Provider)      │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                    Apache Kafka                             │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  domain-events  │ │ projection-     │ │ integration-    │ │
│  │     topic       │ │ rebuild topic   │ │  events topic   │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Data Layer                                   │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │ Orleans State   │ │   Read Models   │ │ Search Engine   │ │
│  │  (PostgreSQL)   │ │  (PostgreSQL)   │ │ (RediSearch)    │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 架构说明

**重要**：在此架构中，**Apache Kafka 是唯一的事件日志系统**。所有领域事件都通过 Orleans Streams 持久化到 Kafka Topics 中。Data Layer 中的 PostgreSQL 仅用于：

1. **Orleans State**: JournaledGrain 的事件存储 (orleansevents 表)
2. **Read Models**: 查询优化的投影数据
3. **Orleans Clustering**: 集群成员管理

这种设计避免了双重事件存储的复杂性，确保了数据一致性和架构简洁性。

## 🛠️ 技术栈

### 核心框架

- **.NET 9**: 目标框架版本
- **Orleans 9.x**: Actor 模型和分布式系统框架
- **Orleans.EventSourcing**: 官方 Event Sourcing 支持
- **ASP.NET Core 9**: Web API 和 SignalR 实时通信

### 数据存储

- **PostgreSQL**: 主要数据存储解决方案
  - Read Models: 查询优化的读模型
  - Orleans State: Grain 状态持久化
  - Orleans Clustering: 集群成员管理
- **RediSearch**: 全文搜索和复杂查询

### 事件流处理

- **Apache Kafka**: 唯一的事件日志系统
  - 作为 Orleans Streams 的底层存储
  - 所有领域事件的持久化存储
  - 支持事件重放和外部系统集成
  - 高可靠性和水平扩展能力
- **Orleans Streams**: 统一的事件流编程模型
  - 与 Kafka 无缝集成
  - 类型安全的事件处理
  - 自动故障转移和负载均衡

### 监控和可观测性

- **Serilog**: 结构化日志
- **OpenTelemetry**: 分布式追踪
- **Prometheus + Grafana**: 指标监控
- **Health Checks**: 健康检查

## 📁 项目结构

```
Curio.Api/
├── src/
│   ├── Curio.Api/                          # Web API层
│   │   ├── Controllers/                    # REST API控制器
│   │   ├── Hubs/                          # SignalR实时通信
│   │   ├── Middleware/                    # 中间件
│   │   └── Program.cs                     # 应用程序入口
│   │
│   ├── Curio.Application/                  # 应用服务层
│   │   ├── Commands/                      # 命令处理器
│   │   ├── Queries/                       # 查询处理器
│   │   ├── EventHandlers/                 # 事件处理器
│   │   ├── Services/                      # 应用服务
│   │   └── DTOs/                          # 数据传输对象
│   │
│   ├── Curio.Domain/                       # 领域层
│   │   ├── Aggregates/                    # 聚合根
│   │   ├── Entities/                      # 实体
│   │   ├── ValueObjects/                  # 值对象
│   │   ├── Events/                        # 领域事件
│   │   ├── Services/                      # 领域服务
│   │   └── Repositories/                  # 仓储接口
│   │
│   ├── Curio.Infrastructure/               # 基础设施层
│   │   ├── Persistence/                   # 数据持久化
│   │   │   ├── EventStore/               # 事件存储实现
│   │   │   ├── ReadModels/               # 读模型实现
│   │   │   └── Repositories/             # 仓储实现
│   │   ├── Messaging/                     # 消息队列
│   │   ├── ExternalServices/              # 外部服务集成
│   │   └── Configuration/                 # 配置管理
│   │
│   ├── Curio.Orleans.Interfaces/          # Orleans接口
│   │   ├── Grains/                       # Grain接口定义
│   │   └── DTOs/                         # Orleans数据传输对象
│   │
│   ├── Curio.Orleans.Grains/              # Orleans Grains实现
│   │   ├── Domain/                       # 领域Grains
│   │   ├── Projection/                   # 投影重建Grains
│   │   └── System/                       # 系统Grains
│   │
│   ├── Curio.Orleans.Silo/                # Orleans Silo主机
│   │   ├── Configuration/                # Silo配置
│   │   └── Program.cs                    # Silo入口点
│   │
│   └── Curio.Shared/                       # 共享组件 (严格控制内容)
│       ├── Events/                       # 共享事件定义
│       ├── Constants/                    # 常量定义
│       ├── Enums/                        # 枚举定义
│       ├── DTOs/                         # 数据传输对象
│       ├── Contracts/                    # 接口契约
│       └── Extensions/                   # 纯粹的扩展方法
│
├── tests/
│   ├── Curio.UnitTests/                   # 单元测试
│   ├── Curio.IntegrationTests/            # 集成测试
│   └── Curio.ArchitectureTests/           # 架构测试
│
└── docker/
    ├── docker-compose.yml                 # 开发环境编排
    ├── docker-compose.prod.yml            # 生产环境编排
    └── Dockerfile                         # 应用镜像构建
```

## � 项目治理规则

### Curio.Shared 项目治理 ⚠️ **高风险区域**

**核心原则**：Curio.Shared 只能包含真正的跨项目共享契约，严禁成为"垃圾场"。

#### ✅ **允许的内容**

```csharp
// 1. 数据传输对象 (DTOs) - 纯数据容器
namespace Curio.Shared.DTOs
{
    public class OrderDto
    {
        public string Id { get; set; }
        public string CustomerId { get; set; }
        public OrderStatus Status { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime CreatedAt { get; set; }
        // 只包含属性，不包含方法
    }
}

// 2. 领域事件定义 - 跨边界契约
namespace Curio.Shared.Events
{
    public abstract class DomainEvent
    {
        public string EventId { get; set; } = Guid.NewGuid().ToString();
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class OrderCreatedEvent : DomainEvent
    {
        public string OrderId { get; set; }
        public string CustomerId { get; set; }
        public decimal TotalAmount { get; set; }
    }
}

// 3. 枚举和常量 - 共享的值定义
namespace Curio.Shared.Enums
{
    public enum OrderStatus
    {
        Pending, Processing, Shipped, Delivered, Cancelled
    }
}

// 4. 接口契约 - Orleans Grain接口
namespace Curio.Shared.Contracts
{
    public interface IOrderGrain : IGrainWithStringKey
    {
        Task<OrderDto> GetOrderAsync();
        Task<OrderDto> CreateOrderAsync(CreateOrderCommand command);
    }
}

// 5. 纯粹的扩展方法 - 无业务逻辑，无状态
namespace Curio.Shared.Extensions
{
    public static class DateTimeExtensions
    {
        public static string ToIso8601String(this DateTime dateTime)
        {
            return dateTime.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
        }
    }
}
```

#### ❌ **严格禁止的内容**

```csharp
// ❌ 业务逻辑 - 应该在Domain层
public static class OrderBusinessLogic
{
    public static decimal CalculateDiscount(OrderDto order) { } // 禁止！
}

// ❌ 基础设施关注点 - 应该在Infrastructure层
public static class DatabaseHelper
{
    public static IDbConnection CreateConnection() { } // 禁止！
}

// ❌ 特定层的工具类 - 应该在对应层
public static class ApiResponseHelper
{
    public static ApiResponse<T> Success<T>(T data) { } // 禁止！应该在Api层
}

// ❌ 配置类 - 应该在Infrastructure层
public class AppSettings { } // 禁止！

// ❌ 验证逻辑 - 应该在Domain或Application层
public static class OrderValidator { } // 禁止！
```

#### 📋 **Shared 项目检查清单**

每次代码审查时必须检查：

1. **单一职责**：每个类只负责数据传输或契约定义
2. **无状态**：不包含任何可变状态或依赖注入
3. **无依赖**：除了基础.NET 类型外，不依赖其他项目
4. **纯数据**：DTOs 只包含属性，不包含方法
5. **版本兼容**：变更时考虑向后兼容性

### Application vs Grains 职责边界

#### ✅ **Application 层职责**

```csharp
// 应用服务 - 编排和协调
public class OrderApplicationService
{
    private readonly IGrainFactory _grainFactory;
    private readonly IOrderQueryService _queryService;

    public async Task<OrderDto> CreateOrderAsync(CreateOrderRequest request)
    {
        // 1. 输入验证和转换
        var command = new CreateOrderCommand
        {
            OrderId = Guid.NewGuid().ToString(),
            CustomerId = request.CustomerId,
            Items = request.Items
        };

        // 2. 调用Domain (Grain)
        var orderGrain = _grainFactory.GetGrain<IOrderGrain>(command.OrderId);
        var result = await orderGrain.CreateOrderAsync(command);

        // 3. 可选的后续操作（如发送通知）
        await _notificationService.SendOrderCreatedNotificationAsync(result);

        return result;
    }
}
```

#### ✅ **Grains 层职责**

```csharp
// Grain - 业务逻辑和状态管理
public class OrderGrain : JournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // 业务规则验证
        if (State.ProcessedCommands.Contains(command.CommandId))
            return State.ToDto();

        // 核心业务逻辑
        var domainEvents = State.CreateOrder(command);

        // 事件持久化
        foreach (var domainEvent in domainEvents)
        {
            RaiseEvent(domainEvent);
        }

        await ConfirmEvents();
        return State.ToDto();
    }
}
```

#### ❌ **常见的职责混乱**

```csharp
// ❌ Application层包含业务逻辑
public class OrderApplicationService
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderRequest request)
    {
        // 业务规则不应该在Application层！
        if (request.TotalAmount > 10000 && !request.IsVipCustomer)
        {
            throw new BusinessException("Large orders require VIP status");
        }

        // 复杂的计算逻辑不应该在Application层！
        var discount = CalculateComplexDiscount(request);

        // 这些都应该在Grain中处理
    }
}

// ❌ Grain层处理基础设施关注点
public class OrderGrain : JournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // HTTP调用不应该在Grain中！
        var customerInfo = await _httpClient.GetAsync($"/customers/{command.CustomerId}");

        // 直接数据库访问不应该在Grain中！
        var inventory = await _dbContext.Inventory.FindAsync(command.ProductId);

        // 这些都应该通过Application层或其他Grain处理
    }
}
```

## �🔄 数据流设计

### 写入流程 (Command Side)

```
HTTP Request → Controller → Application Service → Orleans Grain
                                                      ↓
Domain Logic Processing → State Change → Domain Events → Orleans Stream (Kafka)
                                                              ↓
                                                    Kafka Topics → Event Handlers
                                                              ↓
                                                    Read Model Updates + External Integration
```

### 读取流程 (Query Side)

```
HTTP Request → Controller → Query Service → Read Model DB → Response
```

### 事件处理流程

```
Orleans Grain → Orleans Stream (Kafka) → Kafka Topics
                                              ↓
                                    ┌─────────┼─────────┐
                                    ↓         ↓         ↓
                            Orleans Grains  External   Analytics
                            (Projections)   Systems    Services
```

## 🏗️ 核心组件设计

### 1. Orleans Event Sourcing Grains

```csharp
public interface IOrderGrain : IGrainWithStringKey
{
    Task<OrderDto> GetOrderAsync();
    Task CreateOrderAsync(CreateOrderCommand command);
    Task UpdateOrderStatusAsync(UpdateOrderStatusCommand command);
}

// 使用弹性事件发布的JournaledGrain
public class OrderGrain : ResilientJournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // 检查幂等性
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return State.ToDto();
        }

        // 业务逻辑处理 - 生成领域事件
        var domainEvents = State.CreateOrder(command);

        // 使用JournaledGrain的事件机制
        // 基类会自动处理弹性发布（重试、死信队列等）
        foreach (var domainEvent in domainEvents)
        {
            RaiseEvent(domainEvent);
        }

        // 确认并持久化所有事件
        await ConfirmEvents();

        return State.ToDto();
    }

    // 状态重建逻辑
    protected override void TransitionState(OrderState state, DomainEvent @event)
    {
        switch (@event)
        {
            case OrderCreatedEvent orderCreated:
                state.ApplyEvent(orderCreated);
                break;
            case OrderStatusChangedEvent statusChanged:
                state.ApplyEvent(statusChanged);
                break;
            // 其他事件处理...
        }
    }
}
```

### 2. Kafka 作为唯一事件日志

```yaml
# Kafka Topic配置
domain-events:
  partitions: 12
  replication-factor: 3
  retention.ms: 604800000 # 7天保留
  cleanup.policy: delete

projection-rebuild:
  partitions: 6
  replication-factor: 3
  retention.ms: -1 # 永久保留用于重建
  cleanup.policy: compact
```

**重要说明**：事件发布的具体实现请参考上面"Orleans Event Sourcing Grains"部分的 `JournaledGrain` 示例。本架构统一使用 `JournaledGrain<TState, TEvent>` 模式，避免了传统的 `Grain<TState>` + `WriteStateAsync()` 方式。

Kafka 在此架构中的作用是：

1. **唯一的事件日志存储**：所有领域事件都通过 Orleans Streams 发布到 Kafka
2. **外部系统集成**：其他服务可以直接消费 Kafka 中的事件
3. **投影重建**：支持从任意时间点重建读模型
4. **事件重放**：提供完整的事件历史记录

**架构一致性**：文档中所有的 Grain 实现都基于 `JournaledGrain`，确保了 Event Sourcing 的正确实践。

### 3. CQRS 实现

```csharp
// 命令处理 - 应用服务直接调用Orleans Grain
public class OrderApplicationService
{
    private readonly IGrainFactory _grainFactory;

    public async Task<OrderDto> CreateOrderAsync(CreateOrderRequest request)
    {
        var orderId = Guid.NewGuid().ToString();
        var orderGrain = _grainFactory.GetGrain<IOrderGrain>(orderId);
        return await orderGrain.CreateOrderAsync(request);
    }
}

// 查询处理 - 查询服务直接访问读模型
public class OrderQueryService
{
    private readonly IOrderReadRepository _readRepository;

    public async Task<OrderDto> GetOrderAsync(string orderId)
    {
        return await _readRepository.GetOrderAsync(orderId);
    }

    public async Task<PagedResult<OrderDto>> GetOrdersAsync(OrderQuery query)
    {
        return await _readRepository.GetOrdersAsync(query);
    }
}
```

## 🔄 Orleans Streams + Kafka 集成

### Orleans 配置

```csharp
// Program.cs (Silo)
builder.Host.UseOrleans(siloBuilder =>
{
    siloBuilder
        // PostgreSQL集群管理 - 适用于中小型部署
        // 大规模生产环境建议考虑Consul/etcd等专用方案
        .UseAdoNetClustering(options =>
        {
            options.ConnectionString = postgresConnectionString;
        })
        // Orleans状态存储（用于非Event Sourcing的Grain）
        .AddAdoNetGrainStorage("Default", options =>
        {
            options.ConnectionString = postgresConnectionString;
        })
        // Event Sourcing专用存储
        .AddAdoNetGrainStorage("EventStore", options =>
        {
            options.ConnectionString = postgresConnectionString;
        })
        // 配置JournaledGrain使用EventStore
        .AddLogStorageBasedLogConsistencyProvider("EventSourcing")
        .AddAdoNetGrainStorage("PubSubStore", options =>
        {
            options.ConnectionString = postgresConnectionString;
        })
        // Kafka Streams配置
        .AddKafkaStreams("KafkaStreams", options =>
        {
            options.BrokerList = new[] { "kafka:9092" };
            options.ConsumerGroupId = "orleans-event-streams";
            options.ProducerConfig = new Dictionary<string, object>
            {
                // 可靠性配置
                { "acks", "all" },                          // 等待所有副本确认
                { "enable.idempotence", true },             // 启用幂等性

                // 重试配置 - 避免无限重试
                { "retries", 5 },                           // 最多重试5次
                { "retry.backoff.ms", 1000 },               // 重试间隔1秒
                { "delivery.timeout.ms", 30000 },           // 总超时30秒

                // 批处理优化
                { "batch.size", 16384 },                    // 16KB批处理
                { "linger.ms", 10 },                        // 等待10ms收集更多消息

                // 压缩配置
                { "compression.type", "snappy" },           // 使用Snappy压缩

                // 缓冲区配置
                { "buffer.memory", 33554432 },              // 32MB缓冲区
                { "max.block.ms", 5000 },                   // 最大阻塞5秒

                // 消息大小限制
                { "max.request.size", 1048576 },            // 最大消息1MB

                // 错误处理
                { "request.timeout.ms", 10000 }             // 请求超时10秒
            };

            // 消费者配置
            options.ConsumerConfig = new Dictionary<string, object>
            {
                { "auto.offset.reset", "earliest" },        // 从最早的消息开始
                { "enable.auto.commit", false },            // 手动提交offset
                { "max.poll.records", 100 },                // 每次最多拉取100条
                { "session.timeout.ms", 30000 },            // 会话超时30秒
                { "heartbeat.interval.ms", 10000 }          // 心跳间隔10秒
            };
        });
});
```

### 弹性事件发布组件

为了避免在每个 `JournaledGrain` 中重复实现弹性发布逻辑，我们创建一个可复用的组件：

```csharp
// 弹性事件发布器接口
public interface IResilientEventPublisher
{
    Task PublishAsync<T>(T @event, string streamId) where T : DomainEvent;
    Task PublishBatchAsync<T>(IEnumerable<T> events, string streamId) where T : DomainEvent;
}

// 弹性事件发布器实现
public class ResilientEventPublisher : IResilientEventPublisher
{
    private readonly IStreamProvider _streamProvider;
    private readonly ILogger<ResilientEventPublisher> _logger;
    private readonly ResilientPublishOptions _options;

    public ResilientEventPublisher(
        IStreamProvider streamProvider,
        ILogger<ResilientEventPublisher> logger,
        IOptions<ResilientPublishOptions> options)
    {
        _streamProvider = streamProvider;
        _logger = logger;
        _options = options.Value;
    }

    public async Task PublishAsync<T>(T @event, string streamId) where T : DomainEvent
    {
        var stream = _streamProvider.GetStream<T>("domain-events", streamId);
        await PublishWithRetryAsync(stream, @event);
    }

    public async Task PublishBatchAsync<T>(IEnumerable<T> events, string streamId) where T : DomainEvent
    {
        var stream = _streamProvider.GetStream<T>("domain-events", streamId);

        foreach (var @event in events)
        {
            await PublishWithRetryAsync(stream, @event);
        }
    }

    private async Task PublishWithRetryAsync<T>(IAsyncStream<T> stream, T @event) where T : DomainEvent
    {
        var retryCount = 0;

        while (retryCount < _options.MaxRetries)
        {
            try
            {
                await stream.OnNextAsync(@event);
                return; // 成功发布，退出重试循环
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogWarning(ex,
                    "Failed to publish event to stream (attempt {Attempt}/{MaxRetries}): {EventId}",
                    retryCount, _options.MaxRetries, @event.EventId);

                if (retryCount >= _options.MaxRetries)
                {
                    // 达到最大重试次数，发送到死信队列
                    await SendToDeadLetterQueueAsync(@event, ex);
                    break;
                }

                // 指数退避策略
                var delay = TimeSpan.FromMilliseconds(
                    Math.Pow(2, retryCount) * _options.BaseDelayMs);
                await Task.Delay(delay);
            }
        }
    }

    private async Task SendToDeadLetterQueueAsync<T>(T @event, Exception lastException) where T : DomainEvent
    {
        try
        {
            var deadLetterEvent = new DeadLetterEvent
            {
                OriginalEvent = @event,
                FailureReason = lastException.Message,
                FailureTimestamp = DateTime.UtcNow,
                RetryCount = _options.MaxRetries,
                EventType = typeof(T).Name
            };

            var deadLetterStream = _streamProvider
                .GetStream<DeadLetterEvent>("dead-letter-queue", @event.EventId);

            await deadLetterStream.OnNextAsync(deadLetterEvent);

            _logger.LogError("Event sent to dead letter queue: {EventId}, Reason: {Reason}",
                @event.EventId, lastException.Message);
        }
        catch (Exception dlqEx)
        {
            // 死信队列也失败了，记录到本地备份
            _logger.LogCritical(dlqEx, "Failed to send event to dead letter queue: {EventId}", @event.EventId);
            await WriteToLocalBackupAsync(@event, lastException);
        }
    }

    private async Task WriteToLocalBackupAsync<T>(T @event, Exception exception) where T : DomainEvent
    {
        try
        {
            var backupPath = Path.Combine(_options.BackupDirectory, $"{@event.EventId}.json");
            Directory.CreateDirectory(Path.GetDirectoryName(backupPath));

            var backupData = new
            {
                Event = @event,
                FailureReason = exception.Message,
                FailureTimestamp = DateTime.UtcNow,
                EventType = typeof(T).Name
            };

            await File.WriteAllTextAsync(backupPath, JsonSerializer.Serialize(backupData));
            _logger.LogCritical("Event backed up to local file: {BackupPath}", backupPath);
        }
        catch (Exception backupEx)
        {
            _logger.LogCritical(backupEx, "Failed to backup event to local file: {EventId}", @event.EventId);
        }
    }
}

// 配置选项
public class ResilientPublishOptions
{
    public int MaxRetries { get; set; } = 3;
    public int BaseDelayMs { get; set; } = 1000;
    public string BackupDirectory { get; set; } = "failed-events";
}

// 死信事件定义
public class DeadLetterEvent : DomainEvent
{
    public object OriginalEvent { get; set; }
    public string FailureReason { get; set; }
    public DateTime FailureTimestamp { get; set; }
    public int RetryCount { get; set; }
    public string EventType { get; set; }
}
```

### 基础 JournaledGrain 抽象类

```csharp
// 提供弹性事件发布能力的基础Grain
public abstract class ResilientJournaledGrain<TState, TEvent> : JournaledGrain<TState, TEvent>
    where TState : class, new()
    where TEvent : DomainEvent
{
    private IResilientEventPublisher _eventPublisher;

    public override async Task OnActivateAsync()
    {
        // 获取弹性事件发布器
        _eventPublisher = ServiceProvider.GetRequiredService<IResilientEventPublisher>();
        await base.OnActivateAsync();
    }

    // 重写事件发布逻辑，使用弹性发布器
    protected override async Task OnEventRaised(TEvent @event)
    {
        await _eventPublisher.PublishAsync(@event, this.GetPrimaryKeyString());
    }

    // 提供批量发布能力
    protected async Task PublishEventsAsync(IEnumerable<TEvent> events)
    {
        await _eventPublisher.PublishBatchAsync(events, this.GetPrimaryKeyString());
    }
}
```

### 依赖注入配置

```csharp
// Program.cs - 配置弹性事件发布
builder.Services.Configure<ResilientPublishOptions>(options =>
{
    options.MaxRetries = 3;
    options.BaseDelayMs = 1000;
    options.BackupDirectory = "failed-events";
});

builder.Services.AddSingleton<IResilientEventPublisher, ResilientEventPublisher>();

// Orleans配置
builder.Host.UseOrleans(siloBuilder =>
{
    siloBuilder
        .AddKafkaStreams("KafkaStreams", options =>
        {
            // Kafka配置...
        })
        .ConfigureServices(services =>
        {
            // 在Orleans容器中注册弹性发布器
            services.AddSingleton<IResilientEventPublisher, ResilientEventPublisher>();
            services.Configure<ResilientPublishOptions>(options =>
            {
                options.MaxRetries = 3;
                options.BaseDelayMs = 1000;
                options.BackupDirectory = "failed-events";
            });
        });
});
```

### 弹性发布组件的优势

1. **代码复用**：所有 Grain 都可以继承 `ResilientJournaledGrain` 获得弹性发布能力
2. **关注点分离**：业务 Grain 专注于业务逻辑，发布逻辑由基类处理
3. **统一配置**：重试策略、死信队列等配置统一管理
4. **易于测试**：可以轻松模拟 `IResilientEventPublisher` 进行单元测试
5. **可扩展性**：可以轻松添加新的发布策略（如电路熔断器）

### 监控和告警

```csharp
// 死信队列监控
public class DeadLetterQueueMonitor : BackgroundService
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<DeadLetterQueueMonitor> _logger;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var streamProvider = _grainFactory.GetStreamProvider("KafkaStreams");
        var deadLetterStream = streamProvider.GetStream<DeadLetterEvent>("dead-letter-queue", "monitor");

        await deadLetterStream.SubscribeAsync(async (deadLetterEvent, token) =>
        {
            _logger.LogError("Dead letter event detected: {EventType} - {Reason}",
                deadLetterEvent.EventType, deadLetterEvent.FailureReason);

            // 发送告警通知
            await SendAlertAsync(deadLetterEvent);
        });
    }

    private async Task SendAlertAsync(DeadLetterEvent deadLetterEvent)
    {
        // 实现告警逻辑（邮件、Slack、钉钉等）
    }
}
```

### 领域状态设计

```csharp
// Event Sourcing的状态类
public class OrderState
{
    public string Id { get; private set; }
    public string CustomerId { get; private set; }
    public OrderStatus Status { get; private set; }
    public List<OrderItem> Items { get; private set; } = new();
    public decimal TotalAmount { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }
    public HashSet<string> ProcessedCommands { get; private set; } = new();

    // 业务逻辑方法 - 返回领域事件
    public List<DomainEvent> CreateOrder(CreateOrderCommand command)
    {
        // 业务规则验证
        if (string.IsNullOrEmpty(command.CustomerId))
            throw new DomainException("Customer ID is required");

        if (!command.Items.Any())
            throw new DomainException("Order must have at least one item");

        // 生成领域事件
        var events = new List<DomainEvent>();

        var orderCreatedEvent = new OrderCreatedEvent
        {
            OrderId = command.OrderId,
            CustomerId = command.CustomerId,
            Items = command.Items,
            TotalAmount = command.Items.Sum(x => x.Price * x.Quantity),
            CreatedAt = DateTime.UtcNow,
            CommandId = command.CommandId
        };

        events.Add(orderCreatedEvent);
        return events;
    }

    // 事件应用方法 - 纯函数，无副作用
    public void ApplyEvent(OrderCreatedEvent @event)
    {
        Id = @event.OrderId;
        CustomerId = @event.CustomerId;
        Status = OrderStatus.Pending;
        Items = @event.Items.ToList();
        TotalAmount = @event.TotalAmount;
        CreatedAt = @event.CreatedAt;
        UpdatedAt = @event.CreatedAt;
        ProcessedCommands.Add(@event.CommandId);
    }

    public void ApplyEvent(OrderStatusChangedEvent @event)
    {
        Status = @event.NewStatus;
        UpdatedAt = @event.ChangedAt;
        ProcessedCommands.Add(@event.CommandId);
    }

    public OrderDto ToDto()
    {
        return new OrderDto
        {
            Id = Id,
            CustomerId = CustomerId,
            Status = Status.ToString(),
            Items = Items.Select(x => new OrderItemDto
            {
                ProductId = x.ProductId,
                Quantity = x.Quantity,
                Price = x.Price
            }).ToList(),
            TotalAmount = TotalAmount,
            CreatedAt = CreatedAt,
            UpdatedAt = UpdatedAt
        };
    }
}

// 领域事件定义
public abstract class DomainEvent
{
    public string EventId { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string CommandId { get; set; }
    public string IdempotencyKey { get; set; }
}

public class OrderCreatedEvent : DomainEvent
{
    public string OrderId { get; set; }
    public string CustomerId { get; set; }
    public List<OrderItem> Items { get; set; }
    public decimal TotalAmount { get; set; }
    public DateTime CreatedAt { get; set; }
}

public class OrderStatusChangedEvent : DomainEvent
{
    public string OrderId { get; set; }
    public OrderStatus OldStatus { get; set; }
    public OrderStatus NewStatus { get; set; }
    public DateTime ChangedAt { get; set; }
    public string Reason { get; set; }
}
```

### 投影更新 Grain

```csharp
public class OrderProjectionGrain : Grain, IOrderProjectionGrain
{
    public override async Task OnActivateAsync()
    {
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        var eventStream = streamProvider.GetStream<DomainEvent>("domain-events", "all-orders");

        // 订阅Kafka Stream（通过Orleans Stream API）
        await eventStream.SubscribeAsync(async (domainEvent, token) =>
        {
            await UpdateProjectionAsync(domainEvent);
        });
    }

    private async Task UpdateProjectionAsync(DomainEvent domainEvent)
    {
        switch (domainEvent)
        {
            case OrderCreatedEvent orderCreated:
                await UpdateOrderListViewAsync(orderCreated);
                break;
            case OrderStatusChangedEvent statusChanged:
                await UpdateOrderStatusAsync(statusChanged);
                break;
        }
    }
}
```

### Projection 重建机制

```csharp
public class ProjectionRebuildGrain : Grain, IProjectionRebuildGrain
{
    public async Task StartRebuildAsync(RebuildRequest request)
    {
        var streamProvider = this.GetStreamProvider("KafkaStreams");

        // 使用专用的重建Topic，支持从任意时间点开始
        var rebuildStream = streamProvider.GetStream<DomainEvent>("projection-rebuild", request.ProjectionName);

        // 从Kafka的指定offset开始消费历史事件
        await rebuildStream.SubscribeAsync(async (domainEvent, token) =>
        {
            if (domainEvent.Timestamp >= request.FromDate)
            {
                await ProcessEventForProjection(domainEvent, request.ProjectionName);
            }
        });
    }

    // 支持从Kafka的任意时间点重建
    public async Task RebuildFromTimestampAsync(string projectionName, DateTime fromTimestamp)
    {
        // Kafka支持基于时间戳的offset查找
        // 这是Kafka作为事件日志的核心优势
        var streamProvider = this.GetStreamProvider("KafkaStreams");
        var rebuildStream = streamProvider.GetStream<DomainEvent>("projection-rebuild", projectionName);

        // 配置从指定时间戳开始消费
        await rebuildStream.SubscribeAsync(async (domainEvent, token) =>
        {
            await ProcessEventForProjection(domainEvent, projectionName);
        }, new StreamSubscriptionOptions
        {
            StartFromTimestamp = fromTimestamp
        });
    }
}
```

### 重建管理 API

```csharp
[ApiController]
[Route("api/admin/projections")]
public class ProjectionManagementController : ControllerBase
{
    [HttpPost("{projectionName}/rebuild")]
    public async Task<IActionResult> StartRebuild(string projectionName, [FromBody] RebuildRequest request)
    {
        var rebuildId = Guid.NewGuid().ToString();
        var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);

        await rebuildGrain.StartRebuildAsync(new RebuildRequest
        {
            ProjectionName = projectionName,
            FromDate = request.FromDate,
            ZeroDowntime = request.ZeroDowntime
        });

        return Accepted(new { RebuildId = rebuildId });
    }

    [HttpGet("rebuild/{rebuildId}/status")]
    public async Task<IActionResult> GetRebuildStatus(string rebuildId)
    {
        var rebuildGrain = _grainFactory.GetGrain<IProjectionRebuildGrain>(rebuildId);
        var status = await rebuildGrain.GetRebuildStatusAsync();
        return Ok(status);
    }
}
```

## ⚖️ 事务一致性和 Outbox 模式

### 事务边界问题

在使用 Orleans.EventSourcing 的架构中，事件持久化和状态更新是原子的，但与 Kafka 发布仍然是分离的：

```csharp
public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
{
    // 1. 业务逻辑处理
    var domainEvents = State.CreateOrder(command);

    // 2. 使用JournaledGrain的事件机制（原子操作）
    foreach (var domainEvent in domainEvents)
    {
        RaiseEvent(domainEvent); // 持久化事件 + 更新状态
    }
    await ConfirmEvents(); // 确认事件持久化

    // 3. 发布事件到Kafka (在OnEventRaised回调中)
    // 这一步与事件持久化不在同一事务中
}
```

### 解决方案对比

#### 方案 A：Orleans Event Sourcing + 幂等性处理 ⭐⭐⭐⭐ (推荐)

```csharp
public class OrderGrain : JournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // 检查幂等性
        if (State.ProcessedCommands.Contains(command.CommandId))
        {
            return State.ToDto(); // 幂等性返回
        }

        try
        {
            // 1. 业务逻辑处理 - 生成领域事件
            var domainEvents = State.CreateOrder(command);

            // 2. 使用JournaledGrain的事件机制
            foreach (var domainEvent in domainEvents)
            {
                // 设置幂等性键
                domainEvent.IdempotencyKey = $"{this.GetPrimaryKeyString()}-{command.CommandId}";

                // RaiseEvent会原子地：
                // - 持久化事件到EventStore
                // - 应用事件到当前状态
                // - 触发OnEventRaised回调
                RaiseEvent(domainEvent);
            }

            // 3. 确认所有事件已持久化
            await ConfirmEvents();

            return State.ToDto();
        }
        catch
        {
            // Orleans会自动重试或重新激活Grain
            // JournaledGrain会从事件重建状态
            throw;
        }
    }

    // 事件持久化后的回调 - 发布到Kafka
    protected override async Task OnEventRaised(DomainEvent @event)
    {
        var retryCount = 0;
        const int maxRetries = 3;

        while (retryCount < maxRetries)
        {
            try
            {
                await _eventStream.OnNextAsync(@event);
                return; // 成功发布，退出重试循环
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogWarning(ex, "Failed to publish event to Kafka (attempt {Attempt}/{MaxRetries}): {EventId}",
                    retryCount, maxRetries, @event.EventId);

                if (retryCount >= maxRetries)
                {
                    // 达到最大重试次数，发送到死信队列
                    await SendToDeadLetterQueue(@event, ex);
                    break;
                }

                // 指数退避策略
                var delay = TimeSpan.FromMilliseconds(Math.Pow(2, retryCount) * 1000);
                await Task.Delay(delay);
            }
        }
    }

    private async Task SendToDeadLetterQueue(DomainEvent @event, Exception lastException)
    {
        try
        {
            var deadLetterEvent = new DeadLetterEvent
            {
                OriginalEvent = @event,
                FailureReason = lastException.Message,
                FailureTimestamp = DateTime.UtcNow,
                GrainId = this.GetPrimaryKeyString(),
                RetryCount = 3
            };

            // 发送到死信队列Topic
            var deadLetterStream = this.GetStreamProvider("KafkaStreams")
                .GetStream<DeadLetterEvent>("dead-letter-queue", this.GetPrimaryKeyString());

            await deadLetterStream.OnNextAsync(deadLetterEvent);

            _logger.LogError("Event sent to dead letter queue: {EventId}, Reason: {Reason}",
                @event.EventId, lastException.Message);
        }
        catch (Exception dlqEx)
        {
            // 死信队列也失败了，记录到本地日志
            _logger.LogCritical(dlqEx, "Failed to send event to dead letter queue: {EventId}", @event.EventId);

            // 可以考虑写入本地文件或数据库作为最后的备份
            await WriteToLocalBackup(@event, lastException);
        }
    }

    private async Task WriteToLocalBackup(DomainEvent @event, Exception exception)
    {
        // 写入本地文件系统作为最后的备份
        var backupPath = Path.Combine("failed-events", $"{@event.EventId}.json");
        Directory.CreateDirectory(Path.GetDirectoryName(backupPath));

        var backupData = new
        {
            Event = @event,
            FailureReason = exception.Message,
            FailureTimestamp = DateTime.UtcNow,
            GrainId = this.GetPrimaryKeyString()
        };

        await File.WriteAllTextAsync(backupPath, JsonSerializer.Serialize(backupData));
        _logger.LogCritical("Event backed up to local file: {BackupPath}", backupPath);
    }

    // 状态重建逻辑
    protected override void TransitionState(OrderState state, DomainEvent @event)
    {
        switch (@event)
        {
            case OrderCreatedEvent orderCreated:
                state.ApplyEvent(orderCreated);
                break;
            case OrderStatusChangedEvent statusChanged:
                state.ApplyEvent(statusChanged);
                break;
        }
    }
}

// 事件处理器需要处理幂等性
public class OrderProjectionHandler
{
    public async Task HandleAsync(OrderCreatedEvent @event)
    {
        // 检查幂等性键，避免重复处理
        if (await _processedEventRepository.ExistsAsync(@event.IdempotencyKey))
        {
            return; // 已处理过，跳过
        }

        // 处理事件
        await UpdateProjectionAsync(@event);

        // 记录已处理
        await _processedEventRepository.AddAsync(@event.IdempotencyKey);
    }
}
```

**优势**：

- ✅ 架构简单，无需额外的 Outbox 表
- ✅ 高性能，减少数据库操作
- ✅ 利用 Orleans 的天然容错能力
- ✅ 通过幂等性处理保证最终一致性

**重要提醒**：上述示例展示了基本的实现方式。在实际项目中，**强烈推荐使用本文档前面介绍的 `ResilientJournaledGrain` 基类**，它封装了完整的弹性发布逻辑，让业务 Grain 专注于业务逻辑。参考"弹性事件发布组件"部分的完整实现。

#### 方案 B：Outbox 模式 ⭐⭐⭐ (更安全但复杂)

```csharp
// 注意：这是一个理论示例，JournaledGrain与传统事务的集成比较复杂
// 在实际项目中，强烈推荐使用方案A（Orleans容错 + 幂等性处理）

public class OrderGrain : JournaledGrain<OrderState, DomainEvent>, IOrderGrain
{
    private readonly IOutboxService _outboxService;

    public async Task<OrderDto> CreateOrderAsync(CreateOrderCommand command)
    {
        // 检查幂等性
        if (State.ProcessedCommands.Contains(command.CommandId))
            return State.ToDto();

        // 业务逻辑处理
        var domainEvents = State.CreateOrder(command);

        // 使用JournaledGrain的事件机制
        foreach (var domainEvent in domainEvents)
        {
            RaiseEvent(domainEvent);
        }

        // 确认事件持久化
        await ConfirmEvents();

        // 异步发布到Outbox（最终一致性）
        _ = Task.Run(async () =>
        {
            foreach (var domainEvent in domainEvents)
            {
                await _outboxService.AddEventAsync(domainEvent);
            }
        });

        return State.ToDto();
    }
}

// Outbox表结构
CREATE TABLE outbox_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    aggregate_id VARCHAR(100) NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    published_at TIMESTAMP NULL,
    published BOOLEAN DEFAULT FALSE
);

// 后台发布服务
public class OutboxPublisher : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var unpublishedEvents = await _outboxRepository.GetUnpublishedAsync();

            foreach (var outboxEvent in unpublishedEvents)
            {
                try
                {
                    await _kafkaProducer.ProduceAsync("domain-events", outboxEvent.EventData);
                    outboxEvent.Published = true;
                    await _outboxRepository.UpdateAsync(outboxEvent);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to publish event {EventId}", outboxEvent.Id);
                }
            }

            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}
```

**优势**：

- ✅ 强一致性保证
- ✅ 零数据丢失
- ✅ 可靠的事件发布

**劣势**：

- ❌ 增加架构复杂性
- ❌ 需要维护额外的 Outbox 表
- ❌ 增加延迟（异步发布）

### 推荐方案

对于 Orleans + Kafka 架构，**推荐方案 A**：

1. **利用 Orleans 的容错机制**
2. **通过幂等性处理保证最终一致性**
3. **保持架构简洁性**
4. **获得更好的性能**

如果业务对数据一致性要求极高，可以考虑方案 B，但需要权衡复杂性和性能的代价。

## 🏗️ 集群管理方案选择

### PostgreSQL 集群管理 (当前方案)

**适用场景**：

- 开发和测试环境
- 中小型生产部署 (< 50 节点)
- 已有 PostgreSQL 基础设施的环境

**优势**：

- ✅ 配置简单，无需额外组件
- ✅ 与现有 PostgreSQL 基础设施集成
- ✅ 事务一致性保证
- ✅ 熟悉的 SQL 调试和监控

**限制**：

- ⚠️ 单点故障风险（PostgreSQL 宕机影响整个集群）
- ⚠️ 脑裂恢复能力有限
- ⚠️ 高并发下的性能瓶颈
- ⚠️ 网络分区处理能力较弱

### 生产环境替代方案

#### 1. Consul 集群管理 ⭐⭐⭐⭐⭐ (推荐)

```csharp
// 大规模生产环境配置
builder.Host.UseOrleans(siloBuilder =>
{
    siloBuilder
        .UseConsulClustering(options =>
        {
            options.Address = new Uri("http://consul:8500");
            options.KvRootFolder = "orleans";
        });
});
```

**优势**：

- ✅ 专为分布式系统设计
- ✅ 强一致性和分区容错
- ✅ 内置健康检查和服务发现
- ✅ 优秀的脑裂处理能力
- ✅ 支持多数据中心

#### 2. etcd 集群管理 ⭐⭐⭐⭐

```csharp
builder.Host.UseOrleans(siloBuilder =>
{
    siloBuilder
        .UseEtcdClustering(options =>
        {
            options.ConnectionString = "http://etcd:2379";
        });
});
```

**优势**：

- ✅ Kubernetes 生态系统原生支持
- ✅ 强一致性保证
- ✅ 高性能和低延迟
- ✅ 成熟的运维工具

#### 3. Azure Service Fabric / AWS ECS 集群管理

```csharp
// Azure环境
builder.Host.UseOrleans(siloBuilder =>
{
    siloBuilder.UseAzureStorageClustering(options =>
    {
        options.ConnectionString = azureStorageConnectionString;
    });
});
```

### 集群管理方案对比

| 方案       | 复杂性 | 性能 | 可靠性 | 脑裂处理 | 适用规模    |
| ---------- | ------ | ---- | ------ | -------- | ----------- |
| PostgreSQL | 低     | 中   | 中     | 弱       | < 50 节点   |
| Consul     | 中     | 高   | 高     | 强       | < 1000 节点 |
| etcd       | 中     | 高   | 高     | 强       | < 1000 节点 |
| 云服务     | 低     | 高   | 高     | 强       | 无限制      |

### 迁移建议

1. **开发阶段**：使用 PostgreSQL，简单快速
2. **测试阶段**：模拟生产环境，测试 Consul/etcd
3. **生产部署**：
   - 小规模：PostgreSQL 可以接受
   - 中大规模：强烈建议 Consul 或 etcd
   - 云环境：优先使用云服务提供商的方案

### 监控和告警

```csharp
// 集群健康监控
builder.Services.AddHealthChecks()
    .AddConsul(options =>
    {
        options.HostName = "consul";
        options.Port = 8500;
    })
    .AddCheck<OrleansClusterHealthCheck>("orleans-cluster");

public class OrleansClusterHealthCheck : IHealthCheck
{
    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken)
    {
        var managementGrain = _grainFactory.GetGrain<IManagementGrain>(0);
        var hosts = await managementGrain.GetHosts();

        var activeHosts = hosts.Count(h => h.Status == SiloStatus.Active);

        if (activeHosts == 0)
            return HealthCheckResult.Unhealthy("No active Orleans silos");

        if (activeHosts < 2)
            return HealthCheckResult.Degraded($"Only {activeHosts} active silos");

        return HealthCheckResult.Healthy($"{activeHosts} active silos");
    }
}
```

## 🚀 部署架构

### 部署组件

- **Curio.Api**: ASP.NET Core Web API 服务
- **Curio.Orleans.Silo**: Orleans 集群节点
- **PostgreSQL**: 数据存储（事件存储 + 读模型 + Orleans 持久化）
- **Apache Kafka**: 事件流处理
- **RediSearch**: 全文搜索引擎

### 部署拓扑

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Load Balancer │    │   Load Balancer │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
┌─────────▼───────┐    ┌─────────▼───────┐    ┌─────────▼───────┐
│   Curio.Api     │    │   Curio.Api     │    │   Curio.Api     │
│   (Instance 1)  │    │   (Instance 2)  │    │   (Instance 3)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌────────────▼────────────┐
                    │    Orleans Cluster      │
                    │                         │
          ┌─────────▼───────┐    ┌─────────────────┐
          │ Curio.Orleans.  │    │ Curio.Orleans.  │
          │ Silo (Node 1)   │    │ Silo (Node 2)   │
          └─────────────────┘    └─────────────────┘
                    │                      │
                    └──────────┬───────────┘
                               │
                    ┌──────────▼──────────┐
                    │   Kafka Cluster     │
                    │   + PostgreSQL      │
                    │   + RediSearch      │
                    └─────────────────────┘
```

## 📊 监控和可观测性

### 日志配置

```csharp
// Program.cs
builder.Host.UseSerilog((context, configuration) =>
{
    configuration
        .ReadFrom.Configuration(context.Configuration)
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .Enrich.WithEnvironmentName()
        .WriteTo.Console()
        .WriteTo.Elasticsearch(new ElasticsearchSinkOptions(new Uri("http://elasticsearch:9200"))
        {
            IndexFormat = "curio-logs-{0:yyyy.MM.dd}",
            AutoRegisterTemplate = true
        });
});
```

### 健康检查

```csharp
builder.Services.AddHealthChecks()
    .AddNpgSql(connectionString, name: "postgres")
    .AddRedis(redisConnectionString, name: "redis")
    .AddKafka(options =>
    {
        options.BootstrapServers = "kafka:9092";
        options.Topic = "health-check";
    }, name: "kafka")
    .AddOrleans(name: "orleans");
```

### 指标收集

```csharp
builder.Services.AddOpenTelemetry()
    .WithTracing(builder => builder
        .AddAspNetCoreInstrumentation()
        .AddNpgsqlInstrumentation()
        .AddRedisInstrumentation()
        .AddJaegerExporter())
    .WithMetrics(builder => builder
        .AddAspNetCoreInstrumentation()
        .AddRuntimeInstrumentation()
        .AddPrometheusExporter());
```

## 🔒 安全考虑

### 认证授权

- **JWT Bearer Token**: API 访问认证
- **OAuth 2.0 / OpenID Connect**: 第三方集成
- **Role-Based Access Control**: 基于角色的权限控制

### 数据安全

- **数据加密**: 敏感数据字段加密存储
- **传输加密**: HTTPS/TLS 通信
- **审计日志**: 完整的操作审计追踪

### API 安全

- **Rate Limiting**: API 调用频率限制
- **CORS 配置**: 跨域请求控制
- **Input Validation**: 输入数据验证和清理

## 🧪 测试策略

### 测试金字塔

```
                    /\
                   /  \
                  / E2E \
                 /______\
                /        \
               /Integration\
              /__________\
             /            \
            /  Unit Tests  \
           /________________\
```

### 测试类型

1. **单元测试**: 领域逻辑、业务规则测试
2. **集成测试**: API 端点、数据库交互测试
3. **架构测试**: 架构约束和依赖关系验证
4. **性能测试**: 负载测试和压力测试

## 📈 性能优化

### 缓存策略

- **Redis 缓存**: 热点数据缓存
- **Orleans 内存缓存**: Grain 状态缓存
- **HTTP 缓存**: API 响应缓存

### 数据库优化

- **读写分离**: CQRS 天然支持
- **索引优化**: 针对查询模式优化索引
- **连接池**: 数据库连接池配置

### Orleans 优化

- **Grain 放置策略**: 优化 Grain 分布·
- **状态持久化**: 合理的持久化策略
- **流处理**: 高效的事件流处理

## 🔧 开发环境

- **Docker Compose**: 本地开发环境
- **Hot Reload**: 开发时热重载
- **Database Migrations**: EF Core 迁移

## 🎯 Kafka 作为唯一事件日志的架构优势

### 单一事实来源 (Single Source of Truth)

- **消除数据冗余**: 事件只存储在 Kafka 中，避免双写问题
- **原子性保证**: Orleans Stream Provider 确保事件发布的原子性
- **一致性保证**: 所有消费者都从同一个事件流读取数据

### 简化的架构设计

```
简化前：Orleans Grain → PostgreSQL Event Store + Kafka Topics (双写)
简化后：Orleans Grain → Kafka Topics (单写)
```

### 高性能和可扩展性

- **减少写入延迟**: 移除了 PostgreSQL 写入步骤
- **水平扩展**: Kafka 分区支持并行处理
- **高吞吐量**: Kafka 的高性能写入能力

### 强大的事件重放能力

- **基于时间戳重放**: Kafka 支持从任意时间点开始消费
- **分区并行重建**: 多个分区可以并行重建投影
- **增量重建**: 支持增量更新而非全量重建

### 统一的编程模型

- **类型安全**: Orleans Stream API 提供强类型的事件处理
- **简化开发**: 开发者只需学习 Orleans Stream API，底层自动使用 Kafka
- **自动故障转移**: Orleans 集群的容错能力与 Kafka 的持久化完美结合

### 无缝集成能力

```csharp
// 同一个 Kafka topic，既可以被 Orleans Grain 消费
// 也可以被外部系统消费
Topic: "domain-events"
├── Orleans Grains (通过 Orleans Stream API)
├── External Analytics Service (直接 Kafka Consumer)
└── External Notification Service (直接 Kafka Consumer)
```

### 运维简化

- **减少存储系统**: 不需要维护独立的 Event Store
- **统一监控**: 只需要监控 Kafka 集群
- **备份策略**: Kafka 的复制机制提供数据安全保障

## 📚 最佳实践总结

### Orleans 最佳实践

1. **Grain 设计**: 每个 Grain 代表一个业务聚合
2. **状态管理**: 合理使用 Orleans 持久化
3. **错误处理**: 实现 Grain 级别的错误恢复
4. **性能监控**: 监控 Grain 激活和调用性能
5. **Stream 使用**: 使用 Kafka Stream Provider 获得最佳可靠性

### Event Sourcing 最佳实践

1. **事件设计**: 事件应该表达业务意图
2. **版本控制**: 支持事件模式演进
3. **快照策略**: 大聚合使用快照优化性能
4. **重放机制**: 支持事件重放和投影重建

### CQRS 最佳实践

1. **读写分离**: 彻底分离命令和查询
2. **最终一致性**: 接受读模型的最终一致性
3. **多读模型**: 针对不同查询场景优化
4. **缓存策略**: 合理使用缓存提升查询性能

### DDD 最佳实践

1. **领域建模**: 深入理解业务领域
2. **聚合设计**: 合理划分聚合边界
3. **领域事件**: 使用事件解耦聚合间通信
4. **防腐层**: 隔离外部系统依赖

## 🎯 后续扩展计划

### 短期目标 (1-3 个月)

- [ ] 完成核心领域模型设计
- [ ] 搭建 Orleans + Kafka 集成环境
- [ ] 实现基础的 CQRS 和 Event Sourcing 框架
- [ ] 配置 Orleans Streams 与 Kafka Stream Provider
- [ ] 实现第一个完整的业务流程

### 中期目标 (3-6 个月)

- [ ] 完善监控和可观测性
- [ ] 实现基于 Orleans Streams 的投影重建系统
- [ ] 集成 RediSearch 搜索功能
- [ ] 性能优化和压力测试
- [ ] 完善安全和权限控制

### 长期目标 (6-12 个月)

- [ ] 外部系统集成（直接消费 Kafka topics）
- [ ] 多租户支持
- [ ] 高级分析和报表功能（基于 Kafka 事件流）
- [ ] 服务网格和云原生部署

---

## 📞 联系信息

如有任何架构相关问题或需要进一步讨论，请随时联系架构团队。

**文档维护**: 架构团队
**最后更新**: 2025-08-24
