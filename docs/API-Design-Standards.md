# Curio API 设计规范文档

## 📋 文档信息

- **项目名称**: Curio API
- **规范版本**: v1.0
- **创建日期**: 2025-08-24
- **基于标准**: REST API、OpenAPI 3.1、RFC 7807、JSON:API

## 🎯 设计原则

### 核心原则

1. **一致性**: 统一的命名、结构和行为模式，**所有 API 响应使用 `{ success, code, message, data }` 格式**
2. **可预测性**: 开发者能够直观理解 API 行为
3. **向后兼容**: 版本升级不破坏现有客户端
4. **自描述性**: API 响应包含足够的元数据
5. **安全优先**: 默认安全，最小权限原则

### RESTful 设计

- 使用 HTTP 动词表达操作意图
- 资源导向的 URL 设计
- 无状态通信
- 统一接口约束

#### HTTP 方法语义

| 方法   | 用途     | 幂等性 | 推荐响应           | 说明                       |
| ------ | -------- | ------ | ------------------ | -------------------------- |
| GET    | 获取资源 | ✅     | 200 OK             | 查询操作，不修改服务器状态 |
| POST   | 创建资源 | ❌     | 201 Created        | 创建新资源，非幂等         |
| PUT    | 完整更新 | ✅     | 200 OK             | 替换整个资源               |
| PATCH  | 部分更新 | ❌     | 200 OK             | 修改资源的部分字段         |
| DELETE | 删除资源 | ✅     | **204 No Content** | 删除资源，推荐无响应体     |

## 🌐 URL 设计规范

### 基础结构

```
https://api.curio.com/api/{resource}/{id}/{sub-resource}
```

### 命名约定

```http
# ✅ 正确示例
GET /api/orders                       # 获取订单列表
GET /api/orders/123                   # 获取特定订单
POST /api/orders                      # 创建订单
PUT /api/orders/123                   # 更新整个订单
PATCH /api/orders/123                 # 部分更新订单
DELETE /api/orders/123                # 删除订单

# 子资源
GET /api/orders/123/items             # 获取订单项
POST /api/orders/123/items            # 添加订单项
GET /api/orders/123/payments          # 获取订单支付记录

# ❌ 错误示例
GET /api/getOrders                    # 动词不应出现在URL中
GET /api/order                        # 应使用复数形式
GET /api/Orders                       # 应使用小写
```

### 查询参数规范

```http
# 分页
GET /api/orders?page=1&size=20&sort=createdAt:desc

# 过滤
GET /api/orders?status=pending&customerId=123

# 字段选择
GET /api/orders?fields=id,status,totalAmount

# 搜索
GET /api/orders?q=search_term

# 日期范围
GET /api/orders?createdAt[gte]=2025-01-01&createdAt[lt]=2025-02-01
```

## 📊 HTTP 状态码规范

### 成功响应 (2xx)

```http
200 OK              # 成功获取资源
201 Created         # 成功创建资源
202 Accepted        # 请求已接受，异步处理中
204 No Content      # 成功执行，无返回内容（如DELETE）
```

### 客户端错误 (4xx)

```http
400 Bad Request     # 请求格式错误
401 Unauthorized    # 未认证
403 Forbidden       # 已认证但无权限
404 Not Found       # 资源不存在
405 Method Not Allowed  # HTTP方法不支持
409 Conflict        # 资源冲突
422 Unprocessable Entity  # 请求格式正确但语义错误
429 Too Many Requests     # 请求频率超限
```

### 服务器错误 (5xx)

```http
500 Internal Server Error  # 服务器内部错误
502 Bad Gateway           # 网关错误
503 Service Unavailable   # 服务不可用
504 Gateway Timeout       # 网关超时
```

## 📝 请求/响应格式

### 标准请求格式

```json
{
  "customerId": "123",
  "items": [
    {
      "productId": "456",
      "quantity": 2,
      "price": 29.99
    }
  ]
}
```

### 成功响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": "789",
    "customerId": "123",
    "status": "pending",
    "totalAmount": 59.98,
    "createdAt": "2025-08-24T10:30:00Z",
    "updatedAt": "2025-08-24T10:30:00Z"
  }
}
```

### 创建成功响应

```json
{
  "success": true,
  "code": 201,
  "message": "Order created successfully",
  "data": {
    "id": "789",
    "customerId": "123",
    "status": "pending",
    "totalAmount": 59.98,
    "createdAt": "2025-08-24T10:30:00Z"
  }
}
```

### 删除成功响应

#### 方案一：HTTP 204 No Content (推荐) ⭐⭐⭐⭐⭐

```http
DELETE /api/orders/123
```

```http
HTTP/1.1 204 No Content
Content-Length: 0
```

**优势**：

- ✅ 符合 RESTful 最佳实践
- ✅ 语义清晰：资源已删除，无需返回内容
- ✅ 更高效：减少网络传输
- ✅ 标准化：遵循 HTTP 规范

#### 方案二：HTTP 200 OK (兼容性考虑)

```http
DELETE /api/orders/123
```

```json
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "code": 20003,
  "message": "Order deleted successfully",
  "data": null
}
```

**使用场景**：

- 需要保持响应格式一致性
- 客户端期望统一的响应结构
- 需要返回删除操作的详细信息

#### 实现建议

```csharp
[HttpDelete("{id}")]
public async Task<IActionResult> DeleteOrder(string id)
{
    var exists = await _orderService.ExistsAsync(id);
    if (!exists)
    {
        return NotFound(Error("Order not found", 40401));
    }

    await _orderService.DeleteAsync(id);

    // 推荐：返回204 No Content
    return NoContent();

    // 备选：返回200 OK（如需保持响应格式一致性）
    // return Ok(Success(null, "Order deleted successfully"));
}
```

#### 删除操作的特殊情况

```csharp
// 软删除：资源标记为删除但仍存在
[HttpDelete("{id}")]
public async Task<IActionResult> SoftDeleteOrder(string id)
{
    var order = await _orderService.SoftDeleteAsync(id);

    // 软删除可以返回更新后的资源状态
    return Ok(Success(order, "Order marked as deleted"));
}

// 批量删除：返回删除结果统计
[HttpDelete("batch")]
public async Task<IActionResult> DeleteOrders([FromBody] string[] ids)
{
    var result = await _orderService.DeleteBatchAsync(ids);

    // 批量操作通常需要返回详细结果
    return Ok(Success(new
    {
        deletedCount = result.DeletedCount,
        failedCount = result.FailedCount,
        failedIds = result.FailedIds
    }, "Batch delete completed"));
}
```

### 列表响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "789",
        "customerId": "123",
        "status": "pending",
        "totalAmount": 59.98,
        "createdAt": "2025-08-24T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

## ❌ 错误处理规范

### 业务错误响应

```json
{
  "success": false,
  "code": 40001,
  "message": "Invalid email address",
  "data": null
}
```

### 验证错误响应

```json
{
  "success": false,
  "code": 40001,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "email",
        "message": "Email is required",
        "value": ""
      },
      {
        "field": "items[0].quantity",
        "message": "Quantity must be at least 1",
        "value": 0
      }
    ]
  }
}
```

### 资源不存在错误

```json
{
  "success": false,
  "code": 40004,
  "message": "Order not found",
  "data": null
}
```

### 权限错误响应

```json
{
  "success": false,
  "code": 40301,
  "message": "Access denied",
  "data": null
}
```

### 服务器错误响应

```json
{
  "success": false,
  "code": 50000,
  "message": "Internal server error",
  "data": null
}
```

### 错误码规范

采用"HTTP 状态码 + 两位业务码"的模式，使错误码具备自描述性：

```
2xxxx - 成功响应
  20000 - 操作成功
  20001 - 创建成功
  20002 - 更新成功
  20003 - 删除成功

4xxxx - 客户端错误

400xx - Bad Request (请求错误)
  40001 - 参数验证失败
  40002 - 参数格式错误
  40003 - 参数缺失
  40004 - 参数值无效
  40005 - 请求体格式错误

401xx - Unauthorized (认证错误)
  40101 - Token无效
  40102 - Token过期
  40103 - Token缺失
  40104 - 认证失败

403xx - Forbidden (权限错误)
  40301 - 权限不足
  40302 - 账户被禁用
  40303 - 操作被拒绝
  40304 - 资源访问受限

404xx - Not Found (资源不存在)
  40401 - 资源不存在
  40402 - 接口不存在
  40403 - 页面不存在

409xx - Conflict (资源冲突)
  40901 - 资源已存在
  40902 - 邮箱已被注册
  40903 - 用户名已被占用
  40904 - 状态冲突

422xx - Unprocessable Entity (业务逻辑错误)
  42201 - 业务规则验证失败
  42202 - 状态转换无效
  42203 - 操作条件不满足
  42204 - 数据关联性错误

429xx - Too Many Requests (限流错误)
  42901 - 请求频率超限
  42902 - 并发请求过多
  42903 - 配额已用完

5xxxx - 服务器错误

500xx - Internal Server Error (内部错误)
  50001 - 内部服务器错误
  50002 - 数据库连接失败
  50003 - 外部服务调用失败

502xx - Bad Gateway (网关错误)
  50201 - 上游服务不可用
  50202 - 服务响应超时

503xx - Service Unavailable (服务不可用)
  50301 - 服务维护中
  50302 - 服务过载
```

### 错误码使用指南

#### 如何选择正确的错误码

1. **首先确定 HTTP 状态码**：

   - 400: 客户端请求错误
   - 401: 认证失败
   - 403: 权限不足
   - 404: 资源不存在
   - 409: 资源冲突
   - 422: 业务逻辑错误
   - 429: 请求频率限制
   - 500: 服务器内部错误

2. **然后选择具体的业务码**：
   - 根据具体的错误类型选择对应的两位业务码

#### 常见场景示例

```csharp
// 参数验证失败
return BadRequest(Error("Email format is invalid", 40002));

// Token过期
return Unauthorized(Error("Token has expired", 40102));

// 权限不足
return Forbid(Error("Insufficient permissions", 40301));

// 资源不存在
return NotFound(Error("Order not found", 40401));

// 邮箱已被注册
return Conflict(Error("Email already registered", 40902));

// 业务规则验证失败
return UnprocessableEntity(Error("Cannot cancel shipped order", 42201));

// 请求频率超限
return StatusCode(429, Error("Rate limit exceeded", 42901));

// 内部服务器错误
return StatusCode(500, Error("Internal server error", 50001));
```

#### 错误码映射表

| HTTP 状态码 | 业务错误码范围 | 用途         | 示例                   |
| ----------- | -------------- | ------------ | ---------------------- |
| 400         | 400xx          | 请求参数错误 | 40001-参数验证失败     |
| 401         | 401xx          | 认证错误     | 40101-Token 无效       |
| 403         | 403xx          | 权限错误     | 40301-权限不足         |
| 404         | 404xx          | 资源不存在   | 40401-资源不存在       |
| 409         | 409xx          | 资源冲突     | 40901-资源已存在       |
| 422         | 422xx          | 业务逻辑错误 | 42201-业务规则验证失败 |
| 429         | 429xx          | 限流错误     | 42901-请求频率超限     |
| 500         | 500xx          | 内部错误     | 50001-内部服务器错误   |
| 502         | 502xx          | 网关错误     | 50201-上游服务不可用   |
| 503         | 503xx          | 服务不可用   | 50301-服务维护中       |

## 🔐 认证和授权

### JWT Bearer Token

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### API Key (备选方案)

```http
X-API-Key: your-api-key-here
```

### 权限控制错误

```json
{
  "success": false,
  "code": 40301,
  "message": "Insufficient permissions",
  "data": {
    "requiredPermissions": ["orders:read", "orders:write"]
  }
}
```

## 📅 版本控制

### Header 版本控制 (推荐)

```http
# 请求
GET /api/orders/123
API-Version: 1.0
Accept: application/vnd.curio.v1+json

# 响应
HTTP/1.1 200 OK
API-Version: 1.0
API-Supported-Versions: 1.0, 1.1, 2.0
Content-Type: application/json
```

### 查询参数版本控制 (备选)

```http
GET /api/orders/123?version=1.0
GET /api/orders/123?v=1
```

### URL 版本控制 (兼容性支持)

```http
GET /api/v1/orders/123  # 向后兼容
GET /api/v2/orders/123  # 新版本
```

### 版本策略

- **默认版本**: 不指定版本时使用当前稳定版本
- **版本协商**: 支持多种版本控制方式
- **生命周期管理**: 通过响应头指示版本状态

### 语义化版本控制

```
主版本.次版本.修订版本 (例如: 1.2.3)

1.x.x - 破坏性变更
x.1.x - 向后兼容的功能添加
x.x.1 - 向后兼容的问题修复
```

### 版本废弃通知

```http
# 响应头
API-Version: 1.0
API-Deprecated-Versions: 0.9
Sunset: Wed, 31 Dec 2025 23:59:59 GMT
Warning: 299 - "API version 1.0 will be deprecated on 2025-12-31"
```

## 🔍 过滤、排序和分页

### 简单查询 - GET 方式

```http
# 简单过滤
GET /api/orders?status=pending

# 多值过滤
GET /api/orders?status=pending,processing

# 范围过滤
GET /api/orders?totalAmount[gte]=100&totalAmount[lt]=500

# 模糊搜索
GET /api/orders?customerName[like]=john

# 日期过滤
GET /api/orders?createdAt[gte]=2025-01-01T00:00:00Z
```

### 复杂查询 - POST 方式

当查询条件复杂、URL 过长或包含敏感信息时，使用 POST 方式：

```http
POST /api/orders/search
Content-Type: application/json
```

```json
{
  "filters": {
    "status": ["pending", "processing", "shipped"],
    "dateRange": {
      "start": "2025-01-01T00:00:00Z",
      "end": "2025-12-31T23:59:59Z"
    },
    "customer": {
      "region": ["Asia", "Europe"],
      "tier": "Premium",
      "tags": ["VIP", "Enterprise"]
    },
    "amount": {
      "min": 1000,
      "max": 50000
    }
  },
  "sort": [
    { "field": "createdAt", "direction": "desc" },
    { "field": "amount", "direction": "asc" }
  ],
  "pagination": {
    "page": 1,
    "size": 20
  },
  "include": ["customer", "items", "payments"]
}
```

### 全文搜索 - POST 方式

```http
POST /api/products/search
Content-Type: application/json
```

```json
{
  "query": "wireless bluetooth headphones",
  "filters": {
    "category": "Electronics",
    "priceRange": { "min": 50, "max": 300 },
    "brand": ["Sony", "Bose", "Apple"],
    "features": ["noise-cancelling", "waterproof"]
  },
  "facets": ["brand", "price", "rating"],
  "highlight": ["name", "description"],
  "pagination": { "page": 1, "size": 20 }
}
```

### 批量查询 - POST 方式

```http
POST /api/orders/batch-get
Content-Type: application/json
```

```json
{
  "ids": ["order-123", "order-456", "order-789"],
  "fields": ["id", "status", "totalAmount", "createdAt"]
}
```

### 查询方式选择指南

**使用 GET 查询的场景：**

- 简单的过滤条件（1-3 个参数）
- URL 长度 < 2000 字符
- 可以被缓存的查询
- 不包含敏感信息
- 符合 RESTful 语义的资源获取

**使用 POST 查询的场景：**

- 复杂的查询条件（多层嵌套、多个数组）
- URL 会超过长度限制
- 包含敏感信息（如用户 ID 列表）
- 全文搜索查询
- 批量数据获取
- 需要请求体来表达复杂逻辑

### POST 查询响应格式

```json
{
  "success": true,
  "code": 200,
  "message": "Search completed successfully",
  "data": {
    "items": [
      {
        "id": "order-123",
        "customerId": "customer-456",
        "status": "pending",
        "totalAmount": 299.99,
        "createdAt": "2025-08-24T10:30:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrevious": false
    },
    "facets": {
      "status": {
        "pending": 45,
        "processing": 32,
        "shipped": 73
      },
      "region": {
        "Asia": 89,
        "Europe": 61
      }
    },
    "searchMeta": {
      "query": "wireless bluetooth",
      "executionTime": "23ms",
      "totalHits": 150
    }
  }
}
```

### 排序语法

```http
# 单字段排序
GET /api/orders?sort=createdAt:desc

# 多字段排序
GET /api/orders?sort=status:asc,createdAt:desc
```

### 分页参数

```http
# 基于页码的分页
GET /api/orders?page=1&size=20

# 基于游标的分页 (大数据集推荐)
GET /api/orders?cursor=eyJpZCI6MTIzfQ&limit=20
```

## 🚀 性能优化

### 字段选择

```http
# 只返回需要的字段
GET /api/orders?fields=id,status,totalAmount

# 排除不需要的字段
GET /api/orders?exclude=items,payments
```

### 关联数据加载

```http
# 包含关联数据
GET /api/orders?include=customer,items

# 嵌套包含
GET /api/orders?include=customer,items.product
```

### 缓存控制

```http
# 响应头
Cache-Control: public, max-age=300
ETag: "abc123def456"
Last-Modified: Wed, 24 Aug 2025 10:30:00 GMT

# 条件请求
If-None-Match: "abc123def456"
If-Modified-Since: Wed, 24 Aug 2025 10:30:00 GMT
```

## 📊 批量操作

### 批量创建

```json
POST /api/orders/batch
[
  {
    "customerId": "123",
    "items": [{ "productId": "456", "quantity": 2 }]
  },
  {
    "customerId": "789",
    "items": [{ "productId": "101", "quantity": 1 }]
  }
]
```

### 批量更新

```json
PATCH /api/orders/batch
[
  {
    "id": "123",
    "status": "shipped"
  },
  {
    "id": "456",
    "status": "cancelled"
  }
]
```

### 批量响应

```json
{
  "success": true,
  "code": 200,
  "message": "Batch operation completed",
  "data": {
    "successful": [
      {
        "id": "123",
        "customerId": "456",
        "status": "shipped"
      }
    ],
    "failed": [
      {
        "id": "456",
        "error": {
          "code": 42202,
          "message": "Invalid status transition"
        }
      }
    ],
    "summary": {
      "total": 2,
      "successful": 1,
      "failed": 1
    }
  }
}
```

## 🔄 异步操作

### 长时间运行的操作

```http
POST /api/orders/123/process
HTTP/1.1 202 Accepted
Location: /api/jobs/abc123
```

```json
{
  "success": true,
  "code": 202,
  "message": "Request accepted for processing",
  "data": {
    "jobId": "abc123",
    "status": "processing",
    "progress": 0,
    "estimatedCompletion": "2025-08-24T10:35:00Z",
    "statusUrl": "/api/jobs/abc123",
    "cancelUrl": "/api/jobs/abc123/cancel"
  }
}
```

### 作业状态查询

```http
GET /api/jobs/abc123
```

```json
{
  "success": true,
  "code": 200,
  "message": "Job completed successfully",
  "data": {
    "jobId": "abc123",
    "status": "completed",
    "progress": 100,
    "result": {
      "id": "123",
      "customerId": "456",
      "status": "processed"
    },
    "completedAt": "2025-08-24T10:33:00Z"
  }
}
```

## 🔔 Webhook 规范

### Webhook 事件格式

```json
{
  "id": "evt_abc123",
  "type": "order.created",
  "version": "1.0",
  "timestamp": "2025-08-24T10:30:00Z",
  "data": {
    "type": "order",
    "id": "789",
    "attributes": {
      /* order data */
    }
  },
  "meta": {
    "source": "curio-api",
    "environment": "production"
  }
}
```

### Webhook 安全

```http
# 签名验证
X-Curio-Signature: sha256=abc123def456...
X-Curio-Timestamp: **********

# 幂等性
X-Curio-Delivery-Id: delivery_abc123
```

## 📖 OpenAPI 规范

### 基础信息

```yaml
openapi: 3.1.0
info:
  title: Curio API
  version: 1.0.0
  description: Curio平台的RESTful API
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.curio.com/v1
    description: 生产环境
  - url: https://staging-api.curio.com/v1
    description: 测试环境
```

### 组件定义

```yaml
components:
  schemas:
    Order:
      type: object
      required:
        - customerId
        - items
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        customerId:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, processing, shipped, delivered, cancelled]
        totalAmount:
          type: number
          format: decimal
          minimum: 0
        createdAt:
          type: string
          format: date-time
          readOnly: true
        updatedAt:
          type: string
          format: date-time
          readOnly: true

    # 统一的API响应格式
    ApiResponse:
      type: object
      required:
        - success
        - code
        - message
      properties:
        success:
          type: boolean
          description: 操作是否成功
        code:
          type: integer
          description: 业务状态码
        message:
          type: string
          description: 响应消息
        data:
          description: 响应数据，成功时包含具体数据，失败时为null或错误详情
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
        traceId:
          type: string
          description: 请求追踪ID

    # 成功响应（泛型）
    SuccessResponse:
      allOf:
        - $ref: "#/components/schemas/ApiResponse"
        - type: object
          properties:
            success:
              type: boolean
              enum: [true]
            code:
              type: integer
              minimum: 20000
              maximum: 29999

    # 错误响应
    ErrorResponse:
      allOf:
        - $ref: "#/components/schemas/ApiResponse"
        - type: object
          properties:
            success:
              type: boolean
              enum: [false]
            code:
              type: integer
              minimum: 40001
              maximum: 59999
              description: "错误码格式：HTTP状态码 + 两位业务码"
            data:
              oneOf:
                - type: "null"
                - $ref: "#/components/schemas/ValidationErrors"

    # 验证错误详情
    ValidationErrors:
      type: object
      properties:
        errors:
          type: array
          items:
            $ref: "#/components/schemas/ValidationError"

    ValidationError:
      type: object
      required:
        - field
        - message
      properties:
        field:
          type: string
          description: 错误字段名
        message:
          type: string
          description: 错误消息
        value:
          description: 错误的字段值
        code:
          type: string
          description: 错误代码
```

### API 端点示例

```yaml
paths:
  /orders:
    post:
      summary: 创建订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateOrderRequest"
      responses:
        "201":
          description: 订单创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/Order"
              example:
                success: true
                code: 20001
                message: "Order created successfully"
                data:
                  id: "order-123"
                  customerId: "customer-456"
                  status: "pending"
                  totalAmount: 299.99
                timestamp: "2025-08-24T10:30:00Z"
                traceId: "trace-abc123"
        "400":
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                code: 40001
                message: "Validation failed"
                data:
                  errors:
                    - field: "customerId"
                      message: "Customer ID is required"
                      code: "REQUIRED"
                    - field: "items[0].quantity"
                      message: "Quantity must be at least 1"
                      value: 0
                      code: "RANGE"
                timestamp: "2025-08-24T10:30:00Z"
                traceId: "trace-abc123"
        "404":
          description: 资源不存在
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
              example:
                success: false
                code: 40401
                message: "Customer not found"
                data: null
                timestamp: "2025-08-24T10:30:00Z"
                traceId: "trace-abc123"

  /orders/{id}:
    get:
      summary: 获取订单详情
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        "200":
          description: 获取成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessResponse"
                  - type: object
                    properties:
                      data:
                        $ref: "#/components/schemas/Order"
        "404":
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    delete:
      summary: 删除订单
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
      responses:
        "204":
          description: 删除成功，无响应体
        "404":
          description: 订单不存在
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
```

### 响应格式一致性检查

所有 API 响应都必须遵循统一的格式：

#### ✅ 正确的响应格式

```json
// 成功响应
{
  "success": true,
  "code": 20001,
  "message": "Operation completed successfully",
  "data": { /* 具体数据 */ },
  "timestamp": "2025-08-24T10:30:00Z",
  "traceId": "trace-abc123"
}

// 错误响应
{
  "success": false,
  "code": 40401,
  "message": "Resource not found",
  "data": null,
  "timestamp": "2025-08-24T10:30:00Z",
  "traceId": "trace-abc123"
}

// 验证错误响应
{
  "success": false,
  "code": 40001,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "email",
        "message": "Invalid email format",
        "value": "invalid-email",
        "code": "FORMAT"
      }
    ]
  },
  "timestamp": "2025-08-24T10:30:00Z",
  "traceId": "trace-abc123"
}
```

#### ❌ 错误的响应格式

```json
// 不要使用RFC 7807格式（与我们的规范不一致）
{
  "type": "https://example.com/probs/validation-error",
  "title": "Validation Error",
  "status": 400,
  "detail": "The request contains invalid data",
  "instance": "/orders"
}

// 不要使用不一致的字段名
{
  "isSuccess": true,  // 应该是 success
  "statusCode": 200,  // 应该是 code
  "msg": "OK",        // 应该是 message
  "result": {}        // 应该是 data
}
```

## 🧪 测试规范

### 测试用例结构

```http
# 正常流程测试
GET /api/orders/123
Accept: application/json
Authorization: Bearer valid-token

Expected: 200 OK
{
  "success": true,
  "code": 200,
  "message": "Success",
  "data": {
    "id": "123",
    "customerId": "456",
    "status": "pending"
  }
}

# 错误场景测试
GET /api/orders/999
Accept: application/json
Authorization: Bearer valid-token

Expected: 404 Not Found
{
  "success": false,
  "code": 40004,
  "message": "Order not found",
  "data": null
}
```

### 边界条件测试

- 空数据集
- 最大/最小值
- 特殊字符处理
- 并发访问
- 网络超时

## ✅ 数据验证规范

### 输入验证规则

#### 基础数据类型验证

```csharp
// 字符串验证
public class CreateOrderRequest
{
    [Required(ErrorMessage = "Customer ID is required")]
    [StringLength(50, MinimumLength = 1, ErrorMessage = "Customer ID must be between 1 and 50 characters")]
    public string CustomerId { get; set; }

    [Required(ErrorMessage = "Order description is required")]
    [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
    public string Description { get; set; }

    [EmailAddress(ErrorMessage = "Invalid email format")]
    public string CustomerEmail { get; set; }

    [Phone(ErrorMessage = "Invalid phone number format")]
    public string CustomerPhone { get; set; }
}

// 数值验证
public class OrderItemRequest
{
    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be at least 1")]
    public int Quantity { get; set; }

    [Required]
    [Range(0.01, 999999.99, ErrorMessage = "Price must be between 0.01 and 999999.99")]
    public decimal Price { get; set; }
}
```

#### 自定义验证特性

```csharp
// 自定义日期比较验证
public class DateGreaterThanAttribute : ValidationAttribute
{
    private readonly string _comparisonProperty;

    public DateGreaterThanAttribute(string comparisonProperty)
    {
        _comparisonProperty = comparisonProperty;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var currentValue = (DateTime)value;
        var property = validationContext.ObjectType.GetProperty(_comparisonProperty);
        var comparisonValue = (DateTime)property.GetValue(validationContext.ObjectInstance);

        if (currentValue <= comparisonValue)
        {
            return new ValidationResult(ErrorMessage);
        }

        return ValidationResult.Success;
    }
}

// 业务规则验证
public class ValidOrderStatusAttribute : ValidationAttribute
{
    private readonly string[] _validStatuses = { "pending", "processing", "shipped", "delivered", "cancelled" };

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var status = value.ToString().ToLower();
        if (!_validStatuses.Contains(status))
        {
            return new ValidationResult($"Status must be one of: {string.Join(", ", _validStatuses)}");
        }

        return ValidationResult.Success;
    }
}
```

### 数据格式标准

#### 日期时间格式

```json
{
  "createdAt": "2025-08-24T10:30:00Z", // ISO 8601 UTC格式
  "updatedAt": "2025-08-24T18:30:00+08:00", // 带时区信息
  "birthDate": "1990-05-15", // 日期格式 YYYY-MM-DD
  "eventTime": "14:30:00" // 时间格式 HH:mm:ss
}
```

#### 货币和数值格式

```json
{
  "price": 299.99, // 小数点格式，不使用字符串
  "currency": "USD", // ISO 4217货币代码
  "amount": {
    "value": 299.99,
    "currency": "USD"
  },
  "percentage": 15.5, // 百分比使用数值，不带%符号
  "quantity": 10 // 整数类型
}
```

#### 联系信息格式

```json
{
  "email": "<EMAIL>", // 标准邮箱格式
  "phone": "+86-138-0013-8000", // 国际格式，带国家代码
  "website": "https://example.com", // 完整URL格式
  "address": {
    "street": "123 Main St",
    "city": "Beijing",
    "state": "Beijing",
    "postalCode": "100000",
    "country": "CN" // ISO 3166-1 alpha-2国家代码
  }
}
```

### 验证错误响应格式

```json
{
  "success": false,
  "code": 40001,
  "message": "Validation failed",
  "data": {
    "errors": [
      {
        "field": "customerId",
        "code": "REQUIRED",
        "message": "Customer ID is required",
        "value": null
      },
      {
        "field": "items[0].quantity",
        "code": "RANGE",
        "message": "Quantity must be at least 1",
        "value": 0,
        "constraints": {
          "min": 1,
          "max": 2147483647
        }
      },
      {
        "field": "email",
        "code": "FORMAT",
        "message": "Invalid email format",
        "value": "invalid-email",
        "expectedFormat": "<EMAIL>"
      }
    ],
    "summary": {
      "totalErrors": 3,
      "fieldErrors": 3,
      "businessRuleErrors": 0
    }
  }
}
```

## 📖 API 文档生成规范

### OpenAPI/Swagger 配置

#### 基础配置

```csharp
// Program.cs
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Curio API",
        Version = "v1.0",
        Description = "Curio平台的RESTful API文档",
        Contact = new OpenApiContact
        {
            Name = "API Support Team",
            Email = "<EMAIL>",
            Url = new Uri("https://docs.curio.com")
        },
        License = new OpenApiLicense
        {
            Name = "MIT License",
            Url = new Uri("https://opensource.org/licenses/MIT")
        }
    });

    // 包含XML注释
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    c.IncludeXmlComments(xmlPath);

    // 添加认证配置
    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
});
```

### 代码注释规范

#### 控制器注释

```csharp
/// <summary>
/// 订单管理API
/// </summary>
/// <remarks>
/// 提供订单的创建、查询、更新和删除功能
/// </remarks>
[ApiController]
[Route("api/orders")]
[Produces("application/json")]
public class OrdersController : BaseController
{
    /// <summary>
    /// 获取订单列表
    /// </summary>
    /// <param name="query">查询参数</param>
    /// <returns>订单列表</returns>
    /// <response code="200">成功返回订单列表</response>
    /// <response code="400">请求参数错误</response>
    /// <response code="401">未授权访问</response>
    [HttpGet]
    [ProducesResponseType(typeof(ApiResponse<PagedResult<OrderDto>>), 200)]
    [ProducesResponseType(typeof(ApiResponse), 400)]
    [ProducesResponseType(typeof(ApiResponse), 401)]
    public async Task<ApiResponse<PagedResult<OrderDto>>> GetOrders([FromQuery] OrderQuery query)
    {
        var orders = await _orderService.GetOrdersAsync(query);
        return Success(orders);
    }
}
```

#### DTO 注释

```csharp
/// <summary>
/// 订单数据传输对象
/// </summary>
public class OrderDto
{
    /// <summary>
    /// 订单唯一标识符
    /// </summary>
    /// <example>order-123456</example>
    public string Id { get; set; }

    /// <summary>
    /// 客户ID
    /// </summary>
    /// <example>customer-789</example>
    public string CustomerId { get; set; }

    /// <summary>
    /// 订单状态
    /// </summary>
    /// <example>pending</example>
    public string Status { get; set; }

    /// <summary>
    /// 订单总金额
    /// </summary>
    /// <example>299.99</example>
    public decimal TotalAmount { get; set; }

    /// <summary>
    /// 订单创建时间
    /// </summary>
    /// <example>2025-08-24T10:30:00Z</example>
    public DateTime CreatedAt { get; set; }
}
```

### 示例数据标准

#### 统一的示例数据

```csharp
public static class ApiExamples
{
    public static class Orders
    {
        public const string SampleOrderId = "order-123456";
        public const string SampleCustomerId = "customer-789";

        public static readonly CreateOrderRequest CreateRequest = new()
        {
            CustomerId = SampleCustomerId,
            Items = new[]
            {
                new OrderItemRequest
                {
                    ProductId = "product-456",
                    Quantity = 2,
                    Price = 29.99m
                }
            }
        };

        public static readonly OrderDto SampleOrder = new()
        {
            Id = SampleOrderId,
            CustomerId = SampleCustomerId,
            Status = "pending",
            TotalAmount = 59.98m,
            CreatedAt = new DateTime(2025, 8, 24, 10, 30, 0, DateTimeKind.Utc)
        };
    }
}
```

#### 在控制器中使用示例

```csharp
/// <summary>
/// 创建新订单
/// </summary>
/// <param name="request">订单创建请求</param>
/// <returns>创建的订单信息</returns>
/// <remarks>
/// 示例请求:
///
///     POST /api/orders
///     {
///         "customerId": "customer-123",
///         "items": [
///             {
///                 "productId": "product-456",
///                 "quantity": 2,
///                 "price": 29.99
///             }
///         ]
///     }
///
/// </remarks>
/// <response code="201">订单创建成功</response>
/// <response code="400">请求数据无效</response>
/// <response code="422">业务规则验证失败</response>
[HttpPost]
[ProducesResponseType(typeof(ApiResponse<OrderDto>), 201)]
[ProducesResponseType(typeof(ApiResponse), 400)]
[ProducesResponseType(typeof(ApiResponse), 422)]
public async Task<ApiResponse<OrderDto>> CreateOrder([FromBody] CreateOrderRequest request)
{
    var order = await _orderService.CreateOrderAsync(request);
    return Success(order, "Order created successfully");
}
```

### 文档生成最佳实践

#### 项目文件配置

```xml
<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn> <!-- 忽略缺少XML注释的警告 -->
  </PropertyGroup>
</Project>
```

#### 环境配置

```csharp
// Program.cs
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Curio API v1");
        c.RoutePrefix = "docs"; // 访问路径: /docs
        c.DisplayRequestDuration();
        c.EnableTryItOutByDefault();
        c.DocExpansion(DocExpansion.None);
    });
}
```

## 🔄 幂等性设计

### 幂等性概念

幂等性是指同一个操作执行多次与执行一次的效果相同。在分布式系统中，由于网络不稳定、客户端重试等原因，同一个请求可能被发送多次，因此 API 必须支持幂等性以防止重复操作。

### 幂等性分类

#### 天然幂等的操作

- **GET**: 查询操作天然幂等，返回 200 OK
- **PUT**: 完整资源替换，多次执行结果相同，返回 200 OK
- **DELETE**: 删除操作，多次删除同一资源结果相同，**推荐返回 204 No Content**

#### 需要特殊处理的操作

- **POST**: 创建操作，需要幂等性设计
- **PATCH**: 部分更新操作，需要幂等性设计

### 幂等性实现方案

#### 1. Idempotency-Key Header (推荐)

```http
POST /api/orders
Content-Type: application/json
Idempotency-Key: 550e8400-e29b-41d4-a716-446655440000

{
  "customerId": "customer-123",
  "items": [
    {
      "productId": "product-456",
      "quantity": 2,
      "price": 29.99
    }
  ]
}
```

#### 2. 服务端实现逻辑

```csharp
[HttpPost]
public async Task<ApiResponse<OrderDto>> CreateOrder(
    [FromBody] CreateOrderRequest request,
    [FromHeader(Name = "Idempotency-Key")] string idempotencyKey = null)
{
    // 1. 生成或验证幂等性键
    if (string.IsNullOrEmpty(idempotencyKey))
    {
        idempotencyKey = Guid.NewGuid().ToString();
    }

    // 2. 检查缓存中是否已有相同键的结果
    var cachedResult = await _idempotencyCache.GetAsync<ApiResponse<OrderDto>>(idempotencyKey);
    if (cachedResult != null)
    {
        // 返回缓存的结果，包括状态码
        Response.StatusCode = cachedResult.StatusCode;
        return cachedResult.Response;
    }

    try
    {
        // 3. 执行业务逻辑
        var order = await _orderService.CreateOrderAsync(request, idempotencyKey);
        var response = Success(order, "Order created successfully");

        // 4. 缓存结果（包括状态码）
        var cacheEntry = new IdempotencyCacheEntry
        {
            StatusCode = 201,
            Response = response
        };
        await _idempotencyCache.SetAsync(idempotencyKey, cacheEntry, TimeSpan.FromHours(24));

        Response.StatusCode = 201;
        return response;
    }
    catch (BusinessException ex)
    {
        // 5. 业务异常也需要缓存，避免重复处理
        var errorResponse = Error(ex.Message, 40001);
        var cacheEntry = new IdempotencyCacheEntry
        {
            StatusCode = 400,
            Response = errorResponse
        };
        await _idempotencyCache.SetAsync(idempotencyKey, cacheEntry, TimeSpan.FromHours(1));

        Response.StatusCode = 400;
        return errorResponse;
    }
}
```

#### 3. 幂等性缓存实现

```csharp
public interface IIdempotencyCache
{
    Task<T> GetAsync<T>(string key) where T : class;
    Task SetAsync<T>(string key, T value, TimeSpan expiration) where T : class;
    Task RemoveAsync(string key);
}

public class IdempotencyCache : IIdempotencyCache
{
    private readonly IDistributedCache _cache;
    private readonly ILogger<IdempotencyCache> _logger;

    public async Task<T> GetAsync<T>(string key) where T : class
    {
        try
        {
            var cacheKey = $"idempotency:{key}";
            var cachedValue = await _cache.GetStringAsync(cacheKey);

            if (string.IsNullOrEmpty(cachedValue))
                return null;

            return JsonSerializer.Deserialize<T>(cachedValue);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get idempotency cache for key: {Key}", key);
            return null; // 缓存失败不应该影响业务流程
        }
    }

    public async Task SetAsync<T>(string key, T value, TimeSpan expiration) where T : class
    {
        try
        {
            var cacheKey = $"idempotency:{key}";
            var serializedValue = JsonSerializer.Serialize(value);
            var options = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = expiration
            };

            await _cache.SetStringAsync(cacheKey, serializedValue, options);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set idempotency cache for key: {Key}", key);
            // 缓存失败不应该影响业务流程
        }
    }
}

public class IdempotencyCacheEntry
{
    public int StatusCode { get; set; }
    public object Response { get; set; }
}
```

### 幂等性最佳实践

#### 1. 幂等性键的生成规则

```csharp
// 客户端生成（推荐）
var idempotencyKey = Guid.NewGuid().ToString();

// 基于业务数据生成（适用于特定场景）
var idempotencyKey = $"{customerId}-{timestamp}-{operationType}";

// 基于请求内容哈希（确保相同请求有相同键）
var requestHash = ComputeHash(JsonSerializer.Serialize(request));
var idempotencyKey = $"{userId}-{requestHash}";
```

#### 2. 缓存策略

```csharp
// 不同操作类型的缓存时间
public static class IdempotencyTTL
{
    public static readonly TimeSpan OrderCreation = TimeSpan.FromHours(24);
    public static readonly TimeSpan Payment = TimeSpan.FromDays(7);
    public static readonly TimeSpan UserRegistration = TimeSpan.FromHours(1);
    public static readonly TimeSpan DataUpdate = TimeSpan.FromMinutes(30);
}
```

#### 3. 错误处理

```csharp
// 区分不同类型的错误
try
{
    var result = await ProcessRequestAsync(request, idempotencyKey);
    await CacheSuccessResult(idempotencyKey, result);
    return result;
}
catch (ValidationException ex)
{
    // 验证错误：缓存较短时间，允许客户端修正后重试
    await CacheErrorResult(idempotencyKey, ex, TimeSpan.FromMinutes(5));
    throw;
}
catch (BusinessException ex)
{
    // 业务错误：缓存较长时间，避免重复处理
    await CacheErrorResult(idempotencyKey, ex, TimeSpan.FromHours(1));
    throw;
}
catch (SystemException ex)
{
    // 系统错误：不缓存，允许重试
    _logger.LogError(ex, "System error for idempotency key: {Key}", idempotencyKey);
    throw;
}
```

### 关键业务场景示例

#### 1. 订单创建

```http
POST /api/orders
Idempotency-Key: order-create-550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "customerId": "customer-123",
  "items": [{"productId": "product-456", "quantity": 2, "price": 29.99}]
}
```

#### 2. 支付处理

```http
POST /api/payments
Idempotency-Key: payment-550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "orderId": "order-789",
  "amount": 59.98,
  "paymentMethod": "credit_card",
  "cardToken": "tok_1234567890"
}
```

#### 3. 用户注册

```http
POST /api/users
Idempotency-Key: user-register-550e8400-e29b-41d4-a716-446655440000
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe"
}
```

### 响应头规范

```http
HTTP/1.1 201 Created
Content-Type: application/json
Idempotency-Key: 550e8400-e29b-41d4-a716-446655440000
X-Idempotency-Cached: false

{
  "success": true,
  "code": 20001,
  "message": "Order created successfully",
  "data": {
    "id": "order-789",
    "status": "pending",
    "totalAmount": 59.98
  }
}
```

```http
# 重复请求的响应
HTTP/1.1 201 Created
Content-Type: application/json
Idempotency-Key: 550e8400-e29b-41d4-a716-446655440000
X-Idempotency-Cached: true

{
  "success": true,
  "code": 20001,
  "message": "Order created successfully",
  "data": {
    "id": "order-789",
    "status": "pending",
    "totalAmount": 59.98
  }
}
```

### 监控和告警

```csharp
// 幂等性指标监控
public class IdempotencyMetrics
{
    private readonly IMetricsCollector _metrics;

    public void RecordIdempotencyHit(string operation)
    {
        _metrics.Counter("idempotency_cache_hit")
            .WithTag("operation", operation)
            .Increment();
    }

    public void RecordIdempotencyMiss(string operation)
    {
        _metrics.Counter("idempotency_cache_miss")
            .WithTag("operation", operation)
            .Increment();
    }

    public void RecordIdempotencyError(string operation, string errorType)
    {
        _metrics.Counter("idempotency_error")
            .WithTag("operation", operation)
            .WithTag("error_type", errorType)
            .Increment();
    }
}
```

## 📊 监控和可观测性

### 请求追踪

```http
# 请求头
X-Trace-Id: abc123def456
X-Request-Id: req_789xyz

# 响应头
X-Trace-Id: abc123def456
X-Request-Id: req_789xyz
X-Response-Time: 150ms
```

### 健康检查

```http
GET /api/health
```

```json
{
  "success": true,
  "code": 200,
  "message": "System is healthy",
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "timestamp": "2025-08-24T10:30:00Z",
    "checks": {
      "database": {
        "status": "healthy",
        "responseTime": "5ms"
      },
      "kafka": {
        "status": "healthy",
        "responseTime": "2ms"
      },
      "redis": {
        "status": "degraded",
        "responseTime": "100ms",
        "message": "High latency detected"
      }
    }
  }
}
```

## 🔒 安全最佳实践

### 输入验证

- 严格的数据类型验证
- 长度限制
- 格式验证（邮箱、URL 等）
- SQL 注入防护
- XSS 防护

### 输出安全

- 敏感数据脱敏
- 错误信息不泄露内部信息
- 适当的 HTTP 安全头

### 安全头示例

```http
Strict-Transport-Security: max-age=31536000; includeSubDomains
Content-Security-Policy: default-src 'self'
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
```

## 📚 文档和 SDK

### API 文档要求

- 完整的 OpenAPI 规范
- 交互式 API 文档（Swagger UI）
- 代码示例（多种语言）
- 错误代码说明
- 变更日志

### SDK 设计原则

- 与 API 保持一致的命名
- 强类型支持
- 异步操作支持
- 错误处理封装
- 重试机制

## 🚀 部署和发布

### API 版本发布流程

1. **开发阶段**: 功能开发和单元测试
2. **集成测试**: API 集成测试
3. **文档更新**: OpenAPI 规范和文档
4. **预发布**: 在测试环境验证
5. **生产发布**: 灰度发布和监控
6. **版本标记**: Git 标签和发布说明

### 向后兼容性检查

- 不删除现有字段
- 不修改现有字段类型
- 新增字段设为可选
- 不修改现有端点行为
- 保持错误代码一致性

---

## 📞 联系信息

如有任何 API 设计相关问题，请联系：

**API 团队**
**邮箱**: <EMAIL>
**文档**: https://docs.curio.com/api
**最后更新**: 2025-08-24
