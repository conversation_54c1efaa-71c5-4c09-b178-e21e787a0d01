namespace Curio.Shared.Abstractions;

public record ApiResponse<T>
{
    public bool Success { get; init; }
    public int Code { get; init; }
    public string? Message { get; init; }
    public T? Data { get; init; }

    public static ApiResponse<T> CreateSuccess(T data, int code = 200, string? message = "Success") =>
        new() { Success = true, Data = data, Code = code, Message = message };

    public static ApiResponse<T> CreateFailure(int code, string? message) =>
        new() { Success = false, Code = code, Message = message };
}