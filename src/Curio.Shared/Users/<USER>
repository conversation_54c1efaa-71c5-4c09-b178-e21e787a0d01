using System;

namespace Curio.Shared.Users.Commands
{
    // The command carries the plain text password, which will be hashed in the grain.
    public record CreateUserCommand(string Name, string Email, string Password, string? CreatedBy = null);
    public record UpdateUserProfileCommand(string Name);
    public record DeactivateUserCommand(string Reason);
}

namespace Curio.Shared.Users.Events
{
    public abstract record DomainEvent(Guid EventId, DateTime Timestamp);

    // The event carries the hashed password, never the plain text one.
    public record UserCreatedEvent(
        Guid UserId,
        string Name,
        string Email,
        string HashedPassword,
        DateTime CreatedAt
    ) : DomainEvent(Guid.NewGuid(), DateTime.UtcNow);

    public record UserProfileUpdatedEvent(
        Guid UserId,
        string Name,
        DateTime UpdatedAt
    ) : DomainEvent(Guid.NewGuid(), DateTime.UtcNow);

    public record UserDeactivatedEvent(
        Guid UserId,
        string Reason,
        DateTime DeactivatedAt
    ) : DomainEvent(Guid.NewGuid(), DateTime.UtcNow);
}