<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.3" />
    <PackageReference Include="Microsoft.Orleans.Client" Version="9.2.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.0-preview.7.24406.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Curio.Application\Curio.Application.csproj" />
    <ProjectReference Include="..\Curio.Infrastructure\Curio.Infrastructure.csproj" />
  </ItemGroup>

</Project>
