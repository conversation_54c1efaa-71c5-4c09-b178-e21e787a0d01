using Curio.Shared.Abstractions;
using Microsoft.AspNetCore.Mvc;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
public abstract class BaseController : ControllerBase
{
    protected IActionResult HandleApiResponse<T>(ApiResponse<T> response)
    {
        return new ObjectResult(response)
        {
            StatusCode = response.Success ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest
        };
    }

    protected IActionResult ApiResponse<T>(T data, string? message = null, int code = 200)
    {
        var response = Curio.Shared.Abstractions.ApiResponse<T>.CreateSuccess(data, code, message);
        return new ObjectResult(response)
        {
            StatusCode = code
        };
    }

     protected IActionResult Accepted<T>(T data, string? message = null)
    {
        var response = Curio.Shared.Abstractions.ApiResponse<T>.CreateSuccess(data, 202, message);
        return new ObjectResult(response)
        {
            StatusCode = 202
        };
    }

    protected IActionResult Created<T>(T data, string? message = null)
    {
        var response = Curio.Shared.Abstractions.ApiResponse<T>.CreateSuccess(data, 201, message);
        return new ObjectResult(response)
        {
            StatusCode = 201
        };
    }

    protected IActionResult NoContent(string? message = "Operation successful.")
    {
        return new ObjectResult(null)
        {
            StatusCode = 204
        };
    }
}
