using Curio.Application.Services;
using Curio.Shared.Abstractions;
using Curio.Shared.Authentication;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Curio.Api.Controllers;

[Route("api/users")]
public class UsersController : BaseController
{
    private readonly IUserService _userService;

    public UsersController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Registers a new user.
    /// </summary>
    [HttpPost]
    [ProducesResponseType(typeof(ApiResponse<UserDto>), 201)]
    [ProducesResponseType(typeof(ApiResponse<object>), 400)]
    [ProducesResponseType(typeof(ApiResponse<object>), 409)]
    public async Task<IActionResult> Register([FromBody] RegisterRequest request)
    {
        try
        {
            var userDto = await _userService.RegisterUserAsync(request);
            return Created(userDto, "User registered successfully.");
        }
        catch (ArgumentException ex) // Catches verification code errors
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(400, ex.Message))
            {
                StatusCode = 400
            };
        }
        catch (InvalidOperationException ex) // Catches email already exists errors
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(409, ex.Message))
            {
                StatusCode = 409
            };
        }
        catch (Exception ex)
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(500, ex.Message))
            {
                StatusCode = 500
            };
        }
    }

    /// <summary>
    /// Checks if an email address is available.
    /// </summary>
    /// <returns>200 if email is taken, 404 if available.</returns>
    [HttpHead("email-check")]
    [ProducesResponseType(200)]
    [ProducesResponseType(404)]
    public async Task<IActionResult> CheckEmailAvailability([FromQuery] string email)
    {
        var isAvailable = await _userService.IsEmailAvailableAsync(email);
        return isAvailable ? NotFound() : Ok();
    }
}
