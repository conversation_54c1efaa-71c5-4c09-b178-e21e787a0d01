using Curio.Application.Services;
using Curio.Shared.Abstractions;
using Curio.Shared.Authentication;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Curio.Api.Controllers;

[Route("api/auth")]
public class AuthController : BaseController
{
    private readonly IUserService _userService;

    public AuthController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Authenticates a user and returns a JWT.
    /// </summary>
    [HttpPost("login")]
    [ProducesResponseType(typeof(ApiResponse<LoginResponse>), 200)]
    [ProducesResponseType(typeof(ApiResponse<object>), 401)]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        try
        {
            var response = await _userService.LoginAsync(request);
            return ApiResponse(response, "Login successful.");
        }
        catch (UnauthorizedAccessException ex)
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(401, ex.Message))
            {
                StatusCode = 401
            };
        }
        catch (Exception ex)
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(500, ex.Message))
            {
                StatusCode = 500
            };
        }
    }
}
