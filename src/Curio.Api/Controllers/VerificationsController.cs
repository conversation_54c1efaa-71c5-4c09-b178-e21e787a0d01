using Curio.Application.Services;
using Curio.Shared.Abstractions;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Curio.Api.Controllers;

public class EmailRequest
{
    public string Email { get; set; } = string.Empty;
}

[Route("api/verifications")]
public class VerificationsController : BaseController
{
    private readonly IUserService _userService;

    public VerificationsController(IUserService userService)
    {
        _userService = userService;
    }

    /// <summary>
    /// Requests a verification code to be sent to the user's email.
    /// </summary>
    [HttpPost("email/registration")]
    [ProducesResponseType(typeof(ApiResponse<object>), 202)]
    public async Task<IActionResult> RequestRegistrationCode([FromBody] EmailRequest request)
    {
        try
        {
            await _userService.RequestVerificationCodeAsync(request.Email);
            var responseData = new { expiresIn = 300 }; // 5 minutes
            return Accepted(responseData, "Verification code has been sent. Please check your email.");
        }
        catch (Exception ex)
        {
            return new ObjectResult(Curio.Shared.Abstractions.ApiResponse<object>.CreateFailure(500, ex.Message))
            {
                StatusCode = 500
            };
        }
    }
}
