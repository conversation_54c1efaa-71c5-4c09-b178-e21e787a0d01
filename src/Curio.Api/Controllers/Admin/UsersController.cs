using Curio.Application.Services;
using Curio.Shared.Abstractions;
using Curio.Shared.Authentication;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace Curio.Api.Controllers.Admin;

// In a real application, this controller would be protected with an authorization policy.
// [Authorize(Policy = "IsAdmin")]
[ApiController]
[Route("api/admin/users")]
public class UsersController : BaseController
{
    private readonly IAdminUserService _adminUserService;

    public UsersController(IAdminUserService adminUserService)
    {
        _adminUserService = adminUserService;
    }

    [HttpGet]
    public async Task<IActionResult> GetUsers([FromQuery] UserQueryOptions options)
    {
        var users = await _adminUserService.GetUsersAsync(options);
        return ApiResponse(users);
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetUserById(Guid id)
    {
        var user = await _adminUserService.GetUserByIdAsync(id);
        return user is null ? NotFound() : ApiResponse(user);
    }

    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] RegisterRequest request)
    {
        var user = await _adminUserService.CreateUserAsync(request);
        return Created(user);
    }

    [HttpPut("{id:guid}")]
    public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UserDto userDto)
    {
        var updatedUser = await _adminUserService.UpdateUserAsync(id, userDto);
        return ApiResponse(updatedUser);
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        await _adminUserService.DeleteUserAsync(id);
        return NoContent();
    }
}
