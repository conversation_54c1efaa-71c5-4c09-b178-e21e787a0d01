using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Orleans.Hosting;
using System;
using System.Threading.Tasks;

Console.Title = "Curio.Orleans.Silo";

// In a real app, this would come from appsettings.json or environment variables
const string postgresConnectionString = "Host=localhost;Port=5432;Database=curio;Username=orleans;Password=**********";
const string serviceId = "curio-api";
const string clusterId = "curio-cluster-dev";

var host = new HostBuilder()
    .UseOrleans(siloBuilder =>
    {
        siloBuilder
            .Configure<ClusterOptions>(options =>
            {
                options.ClusterId = clusterId;
                options.ServiceId = serviceId;
            })
            .UseAdoNetClustering(options =>
            {
                options.ConnectionString = postgresConnectionString;
                options.Invariant = "Npgsql";
            })
            .AddAdoNetGrainStorage("Default", options =>
            {
                options.ConnectionString = postgresConnectionString;
                options.Invariant = "Npgsql";
            })
            .AddAdoNetGrainStorage("EventStore", options =>
            {
                options.ConnectionString = postgresConnectionString;
                options.Invariant = "Npgsql";
            })
            .AddLogStorageBasedLogConsistencyProvider("EventStore")
            .AddMemoryGrainStorage("PubSubStore") // Needed for streaming metadata
            .AddKafka("KafkaStreams")
                .WithOptions(options =>
                {
                    options.BrokerList = new() { "localhost:9092" };
                    options.ConsumerGroupId = "curio-orleans-streams";
                    options.TopicCreation = new()
                    {
                        AutoCreate = true,
                        Partitions = 1
                    };
                })
                .Build();
    })
    .ConfigureLogging(logging => logging.AddConsole())
    .Build();


await StartSiloAsync(host);

async Task StartSiloAsync(IHost host)
{
    try
    {
        await host.StartAsync();
        Console.WriteLine("\nSilo is running.\nPress Enter to terminate...\n");
        Console.ReadLine();
        await host.StopAsync();
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Silo failed to start: {ex}");
        throw;
    }
}
