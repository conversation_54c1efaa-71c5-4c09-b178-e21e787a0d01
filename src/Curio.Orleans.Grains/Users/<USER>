using Curio.Orleans.Interfaces.Users;
using Microsoft.Extensions.Logging;
using Orleans;
using Orleans.Runtime;
using System;
using System.Threading.Tasks;

namespace Curio.Orleans.Grains.Users;

public class VerificationState
{
    public string Code { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class VerificationGrain : Grain, IVerificationGrain, global::Orleans.Runtime.IRemindable
{
    private readonly IPersistentState<VerificationState> _state;
    private readonly ILogger<VerificationGrain> _logger;
    private const int ExpirationMinutes = 5;

    public VerificationGrain(
        [PersistentState("Verification")] IPersistentState<VerificationState> state,
        ILogger<VerificationGrain> logger)
    {
        _state = state;
        _logger = logger;
    }

    public async Task<int> RequestRegistrationCode()
    {
        var code = new Random().Next(100000, 999999).ToString();
        _state.State.Code = code;
        _state.State.Timestamp = DateTime.UtcNow;

        await _state.WriteStateAsync();

        // In a real application, you would inject an IEmailService and call it here.
        // For now, we log the code to the console for easy testing.
        _logger.LogInformation($"Verification code for {this.GetPrimaryKeyString()}: {code}");

        // Register a reminder to clear the state after expiration.
        await this.RegisterOrUpdateReminder("ClearCodeReminder", TimeSpan.FromMinutes(ExpirationMinutes), TimeSpan.FromMinutes(ExpirationMinutes));

        return ExpirationMinutes * 60;
    }

    public Task<bool> Verify(string code)
    {
        if (string.IsNullOrEmpty(_state.State.Code) || _state.State.Timestamp.AddMinutes(ExpirationMinutes) < DateTime.UtcNow)
        {
            return Task.FromResult(false); // Code expired or not set
        }

        var isValid = _state.State.Code == code;

        // Clear the state after a successful verification to prevent reuse.
        if (isValid)
        {
            ClearStateAsync().ConfigureAwait(false);
        }

        return Task.FromResult(isValid);
    }

    public async Task ReceiveReminder(string reminderName, global::Orleans.Runtime.TickStatus status)
    {
        if (reminderName == "ClearCodeReminder")
        {
            _logger.LogInformation($"Verification code for {this.GetPrimaryKeyString()} expired. Clearing state.");
            await ClearStateAsync();
        }
    }

    private async Task ClearStateAsync()
    {
        await _state.ClearStateAsync();
        var reminder = await this.GetReminder("ClearCodeReminder");
        if (reminder is not null)
        {
            await this.UnregisterReminder(reminder);
        }
    }
}
