using Curio.Domain.Users;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Authentication;
using Curio.Shared.Users.Commands;
using Curio.Shared.Users.Events;
using Orleans.EventSourcing;
using Orleans.Providers;
using System.Threading.Tasks;
using System;

namespace Curio.Orleans.Grains.Users;

[LogConsistencyProvider(ProviderName = "EventStore")]
public class UserGrain : JournaledGrain<UserState, DomainEvent>, IUserGrain
{
    //<inheritdoc />
    public async Task<UserDto> CreateUser(CreateUserCommand command)
    {
        if (State.Id != Guid.Empty)
        {
            throw new InvalidOperationException($"User {this.GetPrimaryKey()} already exists.");
        }

        if (string.IsNullOrWhiteSpace(command.Name))
        {
            throw new ArgumentException("User name cannot be empty.", nameof(command.Name));
        }
        if (string.IsNullOrWhiteSpace(command.Password) || command.Password.Length < 8)
        {
            throw new ArgumentException("Password must be at least 8 characters long.", nameof(command.Password));
        }

        var hashedPassword = BCrypt.Net.BCrypt.HashPassword(command.Password);

        var userCreatedEvent = new UserCreatedEvent(
            UserId: this.GetPrimaryKey(),
            Name: command.Name,
            Email: command.Email,
            HashedPassword: hashedPassword,
            CreatedAt: DateTime.UtcNow
        );

        RaiseEvent(userCreatedEvent);
        await ConfirmEvents();

        return new UserDto(State.Id.ToString(), State.Name, State.Email);
    }

    //<inheritdoc />
    public Task<UserDto?> GetUserProfile()
    {
        if (State.Id == Guid.Empty)
        {
            return Task.FromResult<UserDto?>(null);
        }
        return Task.FromResult<UserDto?>(new UserDto(State.Id.ToString(), State.Name, State.Email));
    }

    //<inheritdoc />
    public Task<bool> ValidatePassword(string password)
    {
        if (State.Id == Guid.Empty || string.IsNullOrWhiteSpace(State.HashedPassword))
        {
            return Task.FromResult(false);
        }

        var isPasswordCorrect = BCrypt.Net.BCrypt.Verify(password, State.HashedPassword);
        return Task.FromResult(isPasswordCorrect);
    }

    //<inheritdoc />
    public async Task<UserDto> UpdateProfile(UpdateUserProfileCommand command)
    {
        if (State.Id == Guid.Empty)
        {
            throw new InvalidOperationException("User does not exist.");
        }

        var profileUpdatedEvent = new UserProfileUpdatedEvent(
            UserId: this.GetPrimaryKey(),
            Name: command.Name,
            UpdatedAt: DateTime.UtcNow
        );

        RaiseEvent(profileUpdatedEvent);
        await ConfirmEvents();

        return new UserDto(State.Id.ToString(), State.Name, State.Email);
    }

    //<inheritdoc />
    public async Task DeactivateUser(DeactivateUserCommand command)
    {
        if (State.Id == Guid.Empty || !State.IsActive)
        {
            return; // Already deactivated or doesn't exist.
        }

        var userDeactivatedEvent = new UserDeactivatedEvent(
            UserId: this.GetPrimaryKey(),
            Reason: command.Reason,
            DeactivatedAt: DateTime.UtcNow
        );

        RaiseEvent(userDeactivatedEvent);
        await ConfirmEvents();
    }

    //<inheritdoc />
    protected override void TransitionState(UserState state, DomainEvent @event)
    {
        switch (@event)
        {
            case UserCreatedEvent e:
                state.Apply(e);
                break;
            case UserProfileUpdatedEvent e:
                state.Apply(e);
                break;
            case UserDeactivatedEvent e:
                state.Apply(e);
                break;
        }
    }
}
