using Curio.Orleans.Interfaces.Users;
using Orleans;
using Orleans.Runtime;
using System.Collections.Generic;
using System.Threading.Tasks;
using System;

namespace Curio.Orleans.Grains.Users;

public class UserEmailIndexState
{
    public Dictionary<string, Guid> EmailToUserIdMap { get; set; } = new();
}

/// <summary>
/// Manages the mapping of user emails to user IDs to ensure uniqueness and allow for lookups.
/// </summary>
public class UserEmailIndexGrain : Grain, IUserEmailIndexGrain
{
    private readonly IPersistentState<UserEmailIndexState> _state;

    public UserEmailIndexGrain([PersistentState("UserEmailIndex")] IPersistentState<UserEmailIndexState> state)
    {
        _state = state;
    }

    public Task<Guid?> GetUserIdForEmail(string email)
    {
        var normalizedEmail = email.ToLowerInvariant();
        if (_state.State.EmailToUserIdMap.TryGetValue(normalizedEmail, out var userId))
        {
            return Task.FromResult<Guid?>(userId);
        }
        return Task.FromResult<Guid?>(null);
    }

    public async Task<bool> AddUser(string email, Guid userId)
    {
        var normalizedEmail = email.ToLowerInvariant();
        if (_state.State.EmailToUserIdMap.ContainsKey(normalizedEmail))
        {
            return false; // Email already exists
        }

        _state.State.EmailToUserIdMap[normalizedEmail] = userId;
        await _state.WriteStateAsync();
        return true;
    }

    public async Task RemoveUser(string email)
    {
        var normalizedEmail = email.ToLowerInvariant();
        if (_state.State.EmailToUserIdMap.Remove(normalizedEmail))
        {
            await _state.WriteStateAsync();
        }
    }
}
