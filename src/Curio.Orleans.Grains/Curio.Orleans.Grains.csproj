﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\Curio.Domain\Curio.Domain.csproj" />
    <ProjectReference Include="..\Curio.Orleans.Interfaces\Curio.Orleans.Interfaces.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.Orleans.EventSourcing" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Sdk" Version="9.2.1" />
    <PackageReference Include="Microsoft.Orleans.Server" Version="9.2.1" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
