using Curio.Shared.Users.Events;
using System;

namespace Curio.Domain.Users;

/// <summary>
/// Represents the state of a User aggregate.
/// This class should only contain state and methods to apply events to that state.
/// Business logic for creating events belongs in the Grain.
/// </summary>
public class UserState
{
    public Guid Id { get; private set; }
    public string Name { get; private set; } = string.Empty;
    public string Email { get; private set; } = string.Empty;
    public string HashedPassword { get; private set; } = string.Empty;
    public bool IsActive { get; private set; }
    public DateTime CreatedAt { get; private set; }
    public DateTime UpdatedAt { get; private set; }

    /// <summary>
    /// Applies the UserCreatedEvent to the current state.
    /// This is a pure function; it only modifies the state in memory.
    /// </summary>
    public void Apply(UserCreatedEvent evt)
    {
        Id = evt.UserId;
        Name = evt.Name;
        Email = evt.Email;
        HashedPassword = evt.HashedPassword;
        IsActive = true;
        CreatedAt = evt.CreatedAt;
        UpdatedAt = evt.CreatedAt;
    }

    public void Apply(UserProfileUpdatedEvent evt)
    {
        Name = evt.Name;
        UpdatedAt = evt.UpdatedAt;
    }

    public void Apply(UserDeactivatedEvent evt)
    {
        IsActive = false;
        UpdatedAt = evt.DeactivatedAt;
    }
}
