using Curio.Application.Services;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Authentication;
using Curio.Shared.Users.Commands;
using Microsoft.Extensions.Logging;
using Orleans;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Curio.Application.Implementation;

public class AdminUserService : IAdminUserService
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<AdminUserService> _logger;

    public AdminUserService(IGrainFactory grainFactory, ILogger<AdminUserService> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    public Task<UserDto> CreateUserAsync(RegisterRequest request)
    {
        // This flow is similar to public registration but initiated by an admin.
        // It bypasses email verification.
        var command = new CreateUserCommand(request.Name, request.Email, request.Password, "Admin");
        var userId = Guid.NewGuid();
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId);
        return userGrain.CreateUser(command);
    }

    public async Task DeleteUserAsync(Guid userId)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId);
        var user = await userGrain.GetUserProfile();
        if (user is null)
            return;

        // Deactivate the user grain
        await userGrain.DeactivateUser(new DeactivateUserCommand("Deactivated by admin."));

        // Remove from the email index so the email can be reused if needed
        var indexGrain = _grainFactory.GetGrain<IUserEmailIndexGrain>("primary");
        await indexGrain.RemoveUser(user.Email);
    }

    public async Task<UserDto?> GetUserByIdAsync(Guid userId)
    {
        // In a real implementation, this should query a read model database.
        _logger.LogInformation("Fetching user {UserId} from grain. Should be from a read model.", userId);
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId);
        return await userGrain.GetUserProfile();
    }

    public Task<PagedResult<UserDto>> GetUsersAsync(UserQueryOptions options)
    {
        // THIS IS A MOCK IMPLEMENTATION.
        // In a real system, this method MUST query a read model database (e.g., PostgreSQL or Elasticsearch)
        // that is populated by event handlers listening to domain events from Kafka.
        // Calling GetAllGrains or iterating through grains for queries is a strong anti-pattern.
        _logger.LogWarning("GetUsersAsync is using a mock implementation. Replace with a read model query.");

        var mockUsers = new List<UserDto>
        {
            new("a1a1a1a1-b2b2-c3c3-d4d4-e5e5e5e5e5e5", "Mock User 1", "<EMAIL>"),
            new("a2a2a2a2-b3b3-c4c4-d5d5-e6e6e6e6e6e6", "Mock User 2", "<EMAIL>"),
        };

        var result = new PagedResult<UserDto>(mockUsers, mockUsers.Count, options.Page, options.Size);
        return Task.FromResult(result);
    }

    public async Task<UserDto> UpdateUserAsync(Guid userId, UserDto userDto)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId);
        var command = new UpdateUserProfileCommand(userDto.Name);
        return await userGrain.UpdateProfile(command);
    }
}
