using Curio.Application.Services;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Authentication;
using Curio.Shared.Users.Commands;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Orleans;
using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Microsoft.IdentityModel.Tokens;

namespace Curio.Application.Implementation;

public class UserService : IUserService
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<UserService> _logger;
    private readonly IConfiguration _configuration;

    public UserService(IGrainFactory grainFactory, ILogger<UserService> logger, IConfiguration configuration)
    {
        _grainFactory = grainFactory;
        _logger = logger;
        _configuration = configuration;
    }

    public async Task<bool> IsEmailAvailableAsync(string email)
    {
        var indexGrain = _grainFactory.GetGrain<IUserEmailIndexGrain>("primary");
        var userId = await indexGrain.GetUserIdForEmail(email);
        return userId is null;
    }

    public async Task RequestVerificationCodeAsync(string email)
    {
        var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(email.ToLowerInvariant());
        await verificationGrain.RequestRegistrationCode();
    }

    public async Task<UserDto> RegisterUserAsync(RegisterRequest request)
    {
        var email = request.Email.ToLowerInvariant();

        // 1. Verify the code
        var verificationGrain = _grainFactory.GetGrain<IVerificationGrain>(email);
        var isCodeValid = await verificationGrain.Verify(request.VerificationCode);
        if (!isCodeValid)
        {
            throw new ArgumentException("Invalid or expired verification code.");
        }

        // 2. Check email uniqueness via index grain
        var indexGrain = _grainFactory.GetGrain<IUserEmailIndexGrain>("primary");
        var existingUserId = await indexGrain.GetUserIdForEmail(email);
        if (existingUserId is not null)
        {
            throw new InvalidOperationException("Email address is already registered.");
        }

        // 3. Create the user
        var userId = Guid.NewGuid();
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId);
        var command = new CreateUserCommand(request.Name, email, request.Password);
        var userDto = await userGrain.CreateUser(command);

        // 4. Update the email index. This is eventually consistent.
        // In a high-consistency scenario, this could be a two-phase commit or a saga.
        // For registration, eventual consistency is generally acceptable.
        await indexGrain.AddUser(email, userId);

        return userDto;
    }

    public async Task<LoginResponse> LoginAsync(LoginRequest request)
    {
        var email = request.Email.ToLowerInvariant();

        // 1. Find user by email
        var indexGrain = _grainFactory.GetGrain<IUserEmailIndexGrain>("primary");
        var userId = await indexGrain.GetUserIdForEmail(email);

        if (userId is null)
        {
            throw new UnauthorizedAccessException("Invalid email or password.");
        }

        // 2. Validate password
        var userGrain = _grainFactory.GetGrain<IUserGrain>(userId.Value);
        var isPasswordValid = await userGrain.ValidatePassword(request.Password);

        if (!isPasswordValid)
        {
            throw new UnauthorizedAccessException("Invalid email or password.");
        }

        var userProfile = await userGrain.GetUserProfile();
        if (userProfile is null)
        {
             throw new Exception("Unable to retrieve user profile after successful login.");
        }

        // 3. Generate JWT
        return GenerateJwtToken(userProfile);
    }

    private LoginResponse GenerateJwtToken(UserDto user)
    {
        var claims = new[]
        {
            new Claim(JwtRegisteredClaimNames.Sub, user.Id),
            new Claim(JwtRegisteredClaimNames.Name, user.Name),
            new Claim(JwtRegisteredClaimNames.Email, user.Email),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            // Add roles here if you have them
        };

        // In a real app, these should come from IConfiguration
        var jwtSettings = _configuration.GetSection("JwtSettings");
        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings["Key"] ?? "a-super-secret-key-that-is-long-enough"));
        var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
        var expires = DateTime.UtcNow.AddMinutes(Convert.ToDouble(jwtSettings["ExpiresInMinutes"] ?? "60"));

        var token = new JwtSecurityToken(
            issuer: jwtSettings["Issuer"],
            audience: jwtSettings["Audience"],
            claims: claims,
            expires: expires,
            signingCredentials: creds
        );

        var tokenHandler = new JwtSecurityTokenHandler();
        var accessToken = tokenHandler.WriteToken(token);

        return new LoginResponse(accessToken, "", (long)expires.Subtract(DateTime.UtcNow).TotalSeconds, user);
    }
}
