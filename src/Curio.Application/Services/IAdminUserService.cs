using Curio.Shared.Authentication;
using System; // Add this line to import the System namespace
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Curio.Application.Services;

// A simplified pagination result for now.
public record PagedResult<T>(List<T> Items, int TotalCount, int Page, int Size);

public class UserQueryOptions
{
    public int Page { get; set; } = 1;
    public int Size { get; set; } = 20;
    public string? SortBy { get; set; }
    public string? SortDirection { get; set; }
    public string? SearchText { get; set; }
}

public interface IAdminUserService
{
    Task<PagedResult<UserDto>> GetUsersAsync(UserQueryOptions options);
    Task<UserDto?> GetUserByIdAsync(Guid userId);
    Task<UserDto> CreateUserAsync(RegisterRequest request); // Can reuse the register request for simplicity
    Task<UserDto> UpdateUserAsync(Guid userId, UserDto userDto); // A simplified update DTO
    Task DeleteUserAsync(Guid userId);
}
