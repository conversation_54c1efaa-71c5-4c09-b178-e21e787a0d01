using Orleans;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// A grain to maintain a unique index of email addresses to user IDs.
/// This is crucial for fast lookups and ensuring email uniqueness.
/// The key for this grain can be a constant, as it acts as a singleton service.
/// </summary>
public interface IUserEmailIndexGrain : IGrainWithStringKey
{
    /// <summary>
    /// Gets the user ID associated with a given email.
    /// </summary>
    /// <param name="email">The email to look up.</param>
    /// <returns>The user ID if found, otherwise null.</returns>
    Task<Guid?> GetUserIdForEmail(string email);

    /// <summary>
    /// Adds a new email-to-userId mapping to the index.
    /// </summary>
    /// <param name="email">The user's email.</param>
    /// <param name="userId">The user's unique ID.</param>
    /// <returns>True if the mapping was added successfully, false if the email already exists.</returns>
    Task<bool> AddUser(string email, Guid userId);

    /// <summary>
    /// Removes an email-to-userId mapping from the index.
    /// </summary>
    /// <param name="email">The email to remove.</param>
    Task RemoveUser(string email);
}
