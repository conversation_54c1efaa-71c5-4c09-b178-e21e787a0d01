using Curio.Shared.Authentication;
using Curio.Shared.Users.Commands;
using Orleans;
using System.Threading.Tasks;
using System;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// Represents a user in the system.
/// This grain is the aggregate root for a user.
/// </summary>
public interface IUserGrain : IGrainWithGuidKey
{
    /// <summary>
    /// Creates a new user.
    /// </summary>
    Task<UserDto> CreateUser(CreateUserCommand command);

    /// <summary>
    /// Gets the user's profile information.
    /// </summary>
    Task<UserDto?> GetUserProfile();

    /// <summary>
    /// Validates the user's password.
    /// </summary>
    Task<bool> ValidatePassword(string password);

    /// <summary>
    /// Updates a user's profile.
    /// </summary>
    Task<UserDto> UpdateProfile(UpdateUserProfileCommand command);

    /// <summary>
    /// Deactivates a user.
    /// </summary>
    Task DeactivateUser(DeactivateUserCommand command);
}
