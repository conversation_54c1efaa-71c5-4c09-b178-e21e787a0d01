using Orleans;

namespace Curio.Orleans.Interfaces.Users;

/// <summary>
/// Handles the generation and verification of one-time codes, keyed by email address.
/// </summary>
public interface IVerificationGrain : IGrainWithStringKey
{
    /// <summary>
    /// Generates and sends a registration code to the email address (the grain's key).
    /// </summary>
    /// <returns>The expiration time in seconds.</returns>
    Task<int> RequestRegistrationCode();

    /// <summary>
    /// Verifies if the provided code is valid.
    /// </summary>
    /// <param name="code">The code to verify.</param>
    /// <returns>True if the code is valid, otherwise false.</returns>
    Task<bool> Verify(string code);
}
